/*
WARNING: Do NOT edit the input and output ports in this file in a text
editor if you plan to continue editing the block that represents it in
the Block Editor! File corruption is VERY likely to occur.
*/
/*
Copyright (C) 1991-2013 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.
*/
(header "symbol" (version "1.2"))
(symbol
	(rect 0 0 248 152)
	(text "PLL_CLK" (rect 97 0 161 16)(font "Arial" (font_size 10)))
	(text "inst" (rect 8 136 25 148)(font "Arial" ))
	(port
		(pt 0 64)
		(input)
		(text "inclk0" (rect 0 0 31 14)(font "Arial" (font_size 8)))
		(text "inclk0" (rect 4 50 29 63)(font "Arial" (font_size 8)))
		(line (pt 0 64)(pt 48 64))
	)
	(port
		(pt 0 80)
		(input)
		(text "areset" (rect 0 0 36 14)(font "Arial" (font_size 8)))
		(text "areset" (rect 4 66 33 79)(font "Arial" (font_size 8)))
		(line (pt 0 80)(pt 48 80))
	)
	(port
		(pt 248 64)
		(output)
		(text "c0" (rect 0 0 14 14)(font "Arial" (font_size 8)))
		(text "c0" (rect 232 50 242 63)(font "Arial" (font_size 8)))
	)
	(drawing
		(text "Cyclone IV E" (rect 172 136 399 283)(font "Arial" ))
		(text "inclk0 frequency: 50.000 MHz" (rect 58 59 239 129)(font "Arial" ))
		(text "Operation Mode: Normal" (rect 58 72 215 155)(font "Arial" ))
		(text "Clk " (rect 59 93 132 197)(font "Arial" ))
		(text "Ratio" (rect 80 93 180 197)(font "Arial" ))
		(text "Ph (dg)" (rect 107 93 243 197)(font "Arial" ))
		(text "DC (%)" (rect 141 93 312 197)(font "Arial" ))
		(text "c0" (rect 62 107 132 225)(font "Arial" ))
		(text "33/10" (rect 80 107 181 225)(font "Arial" ))
		(text "0.00" (rect 113 107 242 225)(font "Arial" ))
		(text "50.00" (rect 145 107 311 225)(font "Arial" ))
		(line (pt 0 0)(pt 249 0))
		(line (pt 249 0)(pt 249 153))
		(line (pt 0 153)(pt 249 153))
		(line (pt 0 0)(pt 0 153))
		(line (pt 56 91)(pt 173 91))
		(line (pt 56 104)(pt 173 104))
		(line (pt 56 118)(pt 173 118))
		(line (pt 56 91)(pt 56 118))
		(line (pt 77 91)(pt 77 118)(line_width 3))
		(line (pt 104 91)(pt 104 118)(line_width 3))
		(line (pt 138 91)(pt 138 118)(line_width 3))
		(line (pt 172 91)(pt 172 118))
		(line (pt 48 48)(pt 215 48))
		(line (pt 215 48)(pt 215 135))
		(line (pt 48 135)(pt 215 135))
		(line (pt 48 48)(pt 48 135))
		(line (pt 247 64)(pt 215 64))
	)
)
