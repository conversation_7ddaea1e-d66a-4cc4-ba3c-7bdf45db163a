# -------------------------------------------------------------------------- #
#
# Copyright (C) 1991-2013 Altera Corporation
# Your use of Altera Corporation's design tools, logic functions 
# and other software and tools, and its AMPP partner logic 
# functions, and any output files from any of the foregoing 
# (including device programming or simulation files), and any 
# associated documentation or information are expressly subject 
# to the terms and conditions of the Altera Program License 
# Subscription Agreement, Altera MegaCore Function License 
# Agreement, or other applicable license agreement, including, 
# without limitation, that your use is for the sole purpose of 
# programming logic devices manufactured by Altera and sold by 
# Altera or its authorized distributors.  Please refer to the 
# applicable agreement for further details.
#
# -------------------------------------------------------------------------- #
#
# Quartus II 64-Bit
# Version 13.0.0 Build 156 04/24/2013 SJ Full Version
# Date created = 16:58:34  February 28, 2024
#
# -------------------------------------------------------------------------- #
#
# Notes:
#
# 1) The default values for assignments are stored in the file:
#		DAC904_assignment_defaults.qdf
#    If this file doesn't exist, see file:
#		assignment_defaults.qdf
#
# 2) Altera recommends that you do not modify this file. This
#    file is updated automatically by the Quartus II software
#    and any changes you make may be lost or overwritten.
#
# -------------------------------------------------------------------------- #


set_global_assignment -name FAMILY "Cyclone IV E"
set_global_assignment -name DEVICE EP4CE6E22C8
set_global_assignment -name TOP_LEVEL_ENTITY DAC904_TOP
set_global_assignment -name ORIGINAL_QUARTUS_VERSION 13.0
set_global_assignment -name PROJECT_CREATION_TIME_DATE "16:58:34  FEBRUARY 28, 2024"
set_global_assignment -name LAST_QUARTUS_VERSION 13.1
set_global_assignment -name PROJECT_OUTPUT_DIRECTORY output_files
set_global_assignment -name MIN_CORE_JUNCTION_TEMP 0
set_global_assignment -name MAX_CORE_JUNCTION_TEMP 85
set_global_assignment -name DEVICE_FILTER_SPEED_GRADE 8
set_global_assignment -name ERROR_CHECK_FREQUENCY_DIVISOR 1
set_global_assignment -name EDA_SIMULATION_TOOL "ModelSim-Altera (Verilog)"
set_global_assignment -name EDA_OUTPUT_DATA_FORMAT "VERILOG HDL" -section_id eda_simulation
set_global_assignment -name POWER_PRESET_COOLING_SOLUTION "23 MM HEAT SINK WITH 200 LFPM AIRFLOW"
set_global_assignment -name POWER_BOARD_THERMAL_MODEL "NONE (CONSERVATIVE)"
set_global_assignment -name PARTITION_NETLIST_TYPE SOURCE -section_id Top
set_global_assignment -name PARTITION_FITTER_PRESERVATION_LEVEL PLACEMENT_AND_ROUTING -section_id Top
set_global_assignment -name PARTITION_COLOR ******** -section_id Top
set_global_assignment -name STRATIX_DEVICE_IO_STANDARD "2.5 V"
set_location_assignment PIN_88 -to SYS_CLK
set_location_assignment PIN_24 -to SYS_RST
set_location_assignment PIN_101 -to DAC_CLK
set_location_assignment PIN_100 -to DAC_DATA[13]
set_location_assignment PIN_99 -to DAC_DATA[12]
set_location_assignment PIN_98 -to DAC_DATA[11]
set_location_assignment PIN_87 -to DAC_DATA[10]
set_location_assignment PIN_86 -to DAC_DATA[9]
set_location_assignment PIN_85 -to DAC_DATA[8]
set_location_assignment PIN_84 -to DAC_DATA[7]
set_location_assignment PIN_83 -to DAC_DATA[6]
set_location_assignment PIN_80 -to DAC_DATA[5]
set_location_assignment PIN_77 -to DAC_DATA[4]
set_location_assignment PIN_76 -to DAC_DATA[3]
set_location_assignment PIN_75 -to DAC_DATA[2]
set_location_assignment PIN_74 -to DAC_DATA[1]
set_location_assignment PIN_73 -to DAC_DATA[0]
set_location_assignment PIN_103 -to PD
set_location_assignment PIN_89 -to KEY_IN[1]
set_location_assignment PIN_90 -to KEY_IN[0]
set_global_assignment -name USE_CONFIGURATION_DEVICE OFF
set_global_assignment -name CRC_ERROR_OPEN_DRAIN OFF
set_global_assignment -name CYCLONEII_RESERVE_NCEO_AFTER_CONFIGURATION "USE AS REGULAR IO"
set_global_assignment -name OUTPUT_IO_TIMING_NEAR_END_VMEAS "HALF VCCIO" -rise
set_global_assignment -name OUTPUT_IO_TIMING_NEAR_END_VMEAS "HALF VCCIO" -fall
set_global_assignment -name OUTPUT_IO_TIMING_FAR_END_VMEAS "HALF SIGNAL SWING" -rise
set_global_assignment -name OUTPUT_IO_TIMING_FAR_END_VMEAS "HALF SIGNAL SWING" -fall
set_global_assignment -name VERILOG_FILE ../ip_core/ROM/ROM_Tri.v
set_global_assignment -name VERILOG_FILE ../rtl/sel_wave.v
set_global_assignment -name VERILOG_FILE ../rtl/key_delay.v
set_global_assignment -name VERILOG_FILE ../rtl/key_con.v
set_global_assignment -name VERILOG_FILE ../rtl/add_32bit.v
set_global_assignment -name VERILOG_FILE ../ip_core/ROM/ROM_Sin.v
set_global_assignment -name VERILOG_FILE ../rtl/DAC904_TOP.v
set_global_assignment -name QIP_FILE ../ip_core/PLL/PLL_CLK.qip
set_global_assignment -name SDC_FILE ../doc/SDC1.sdc
set_location_assignment PIN_91 -to KEY_IN[2]
set_instance_assignment -name PARTITION_HIERARCHY root_partition -to | -section_id Top