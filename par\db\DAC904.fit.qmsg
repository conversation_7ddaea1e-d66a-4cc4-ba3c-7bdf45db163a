{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Fitter" 0 -1 1753967485276 ""}
{ "Info" "IMPP_MPP_USER_DEVICE" "DAC904 EP4CE6E22C8 " "Selected device EP4CE6E22C8 for design \"DAC904\"" {  } {  } 0 119006 "Selected device %2!s! for design \"%1!s!\"" 0 0 "Fitter" 0 -1 1753967485282 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1753967485305 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1753967485305 ""}
{ "Info" "ICUT_CUT_PLL_COMPUTATION_SUCCESS" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1 Cyclone IV E PLL " "Implemented PLL \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1\" as Cyclone IV E PLL type" { { "Info" "ICUT_CUT_YGR_PLL_PARAMETERS_FACTORS" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] 2 5 0 0 " "Implementing clock multiplication of 2, clock division of 5, and phase shift of 0 degrees (0 ps) for PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] port" {  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 46 -1 0 } } { "" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 276 9662 10382 0}  }  } }  } 0 15099 "Implementing clock multiplication of %2!d!, clock division of %3!d!, and phase shift of %4!d! degrees (%5!d! ps) for %1!s! port" 0 0 "Quartus II" 0 -1 1753967485333 ""}  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 46 -1 0 } } { "" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 276 9662 10382 0}  }  } }  } 0 15535 "Implemented %3!s! \"%1!s!\" as %2!s! PLL type" 0 0 "Fitter" 0 -1 1753967485333 ""}
{ "Info" "IFITCC_FITCC_INFO_AUTO_FIT_COMPILATION_ON" "" "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" {  } {  } 0 171003 "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" 0 0 "Fitter" 0 -1 1753967485359 ""}
{ "Warning" "WCPT_FEATURE_DISABLED_POST" "LogicLock " "Feature LogicLock is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." {  } {  } 0 292013 "Feature %1!s! is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." 0 0 "Fitter" 0 -1 1753967485364 ""}
{ "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED" "" "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" { { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE10E22C8 " "Device EP4CE10E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Quartus II" 0 -1 1753967485440 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE15E22C8 " "Device EP4CE15E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Quartus II" 0 -1 1753967485440 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE22E22C8 " "Device EP4CE22E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Quartus II" 0 -1 1753967485440 ""}  } {  } 2 176444 "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" 0 0 "Fitter" 0 -1 1753967485440 ""}
{ "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION" "4 " "Fitter converted 4 user pins into dedicated programming pins" { { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_ASDO_DATA1~ 6 " "Pin ~ALTERA_ASDO_DATA1~ is reserved at location 6" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_ASDO_DATA1~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_ASDO_DATA1~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 2211 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753967485441 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_FLASH_nCE_nCSO~ 8 " "Pin ~ALTERA_FLASH_nCE_nCSO~ is reserved at location 8" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_FLASH_nCE_nCSO~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_FLASH_nCE_nCSO~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 2213 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753967485441 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DCLK~ 12 " "Pin ~ALTERA_DCLK~ is reserved at location 12" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DCLK~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_DCLK~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 2215 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753967485441 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DATA0~ 13 " "Pin ~ALTERA_DATA0~ is reserved at location 13" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DATA0~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_DATA0~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 2217 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753967485441 ""}  } {  } 0 169124 "Fitter converted %1!d! user pins into dedicated programming pins" 0 0 "Fitter" 0 -1 1753967485441 ""}
{ "Warning" "WCUT_CUT_ATOM_PINS_WITH_INCOMPLETE_IO_ASSIGNMENTS" "" "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" {  } {  } 0 15714 "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" 0 0 "Fitter" 0 -1 1753967485442 ""}
{ "Info" "IFSAC_FSAC_RAM_METASTABILITY_INFO" "" "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." {  } {  } 0 176045 "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." 0 0 "Fitter" 0 -1 1753967485443 ""}
{ "Info" "ISTA_SDC_FOUND" "../doc/SDC1.sdc " "Reading SDC File: '../doc/SDC1.sdc'" {  } {  } 0 332104 "Reading SDC File: '%1!s!'" 0 0 "Fitter" 0 -1 1753967485724 ""}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL clocks " "Deriving PLL clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} " "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\}" {  } {  } 0 332110 "%1!s!" 0 0 "Quartus II" 0 -1 1753967485726 ""}  } {  } 0 332110 "%1!s!" 0 0 "Fitter" 0 -1 1753967485726 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_CALL_IS_DELAYED" "" "Clock uncertainty is not calculated until you update the timing netlist." {  } {  } 0 332151 "Clock uncertainty is not calculated until you update the timing netlist." 0 0 "Fitter" 0 -1 1753967485726 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "key_con:u_key_con\|key_delay:u_key2_delay\|kout " "Node: key_con:u_key_con\|key_delay:u_key2_delay\|kout was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1753967485727 "|DAC904_TOP|key_con:u_key_con|key_delay:u_key2_delay|kout"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "key_con:u_key_con\|key_delay:u_key1_delay\|kout " "Node: key_con:u_key_con\|key_delay:u_key1_delay\|kout was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1753967485728 "|DAC904_TOP|key_con:u_key_con|key_delay:u_key1_delay|kout"}
{ "Info" "ISTA_NO_UNCERTAINTY_FOUND" "" "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." {  } {  } 0 332154 "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." 0 0 "Fitter" 0 -1 1753967485729 ""}
{ "Info" "ISTA_USER_TDC_OPTIMIZATION_GOALS" "" "Detected timing requirements -- optimizing circuit to achieve only the specified requirements" {  } {  } 0 332129 "Detected timing requirements -- optimizing circuit to achieve only the specified requirements" 0 0 "Fitter" 0 -1 1753967485730 ""}
{ "Info" "ISTA_REPORT_CLOCKS_INFO" "Found 3 clocks " "Found 3 clocks" { { "Info" "ISTA_REPORT_CLOCKS_INFO" "  Period   Clock Name " "  Period   Clock Name" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753967485730 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "======== ============ " "======== ============" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753967485730 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "  20.000      CLK_50M " "  20.000      CLK_50M" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753967485730 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "   6.060     CLK_165M " "   6.060     CLK_165M" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753967485730 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "  50.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " "  50.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753967485730 ""}  } {  } 0 332111 "%1!s!" 0 0 "Fitter" 0 -1 1753967485730 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_2) " "Automatically promoted node PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_2)" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G8 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G8" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""}  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 80 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { PLL_CLK:u_PLL_CLK|altpll:altpll_component|PLL_CLK_altpll:auto_generated|wire_pll1_clk[0] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 276 9662 10382 0}  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753967485750 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "key_con:u_key_con\|freq_sel  " "Automatically promoted node key_con:u_key_con\|freq_sel " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""}  } { { "../rtl/key_con.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/key_con.v" 19 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { key_con:u_key_con|freq_sel } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 164 9662 10382 0}  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753967485750 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "key_con:u_key_con\|key_delay:u_key2_delay\|kout  " "Automatically promoted node key_con:u_key_con\|key_delay:u_key2_delay\|kout " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "key_con:u_key_con\|key_delay:u_key2_delay\|kout~5 " "Destination node key_con:u_key_con\|key_delay:u_key2_delay\|kout~5" {  } { { "../rtl/key_delay.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/key_delay.v" 32 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { key_con:u_key_con|key_delay:u_key2_delay|kout~5 } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 1357 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Quartus II" 0 -1 1753967485750 ""}  } { { "../rtl/key_delay.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/key_delay.v" 32 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { key_con:u_key_con|key_delay:u_key2_delay|kout } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 372 9662 10382 0}  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753967485750 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "SYS_RST~input (placed in PIN 24 (CLK2, DIFFCLK_1p)) " "Automatically promoted node SYS_RST~input (placed in PIN 24 (CLK2, DIFFCLK_1p))" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G1 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G1" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[31\] " "Destination node add_32bit:u_add_32bit\|add\[31\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[31] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 214 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[20\] " "Destination node add_32bit:u_add_32bit\|add\[20\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[20] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 203 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[21\] " "Destination node add_32bit:u_add_32bit\|add\[21\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[21] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 204 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[22\] " "Destination node add_32bit:u_add_32bit\|add\[22\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[22] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 205 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[23\] " "Destination node add_32bit:u_add_32bit\|add\[23\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[23] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 206 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[24\] " "Destination node add_32bit:u_add_32bit\|add\[24\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[24] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 207 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[25\] " "Destination node add_32bit:u_add_32bit\|add\[25\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[25] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 208 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[26\] " "Destination node add_32bit:u_add_32bit\|add\[26\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[26] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 209 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[27\] " "Destination node add_32bit:u_add_32bit\|add\[27\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[27] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 210 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[28\] " "Destination node add_32bit:u_add_32bit\|add\[28\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[28] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 211 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967485750 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_LIMITED_TO_SUB" "10 " "Non-global destination nodes limited to 10 nodes" {  } {  } 0 176358 "Non-global destination nodes limited to %1!d! nodes" 0 0 "Quartus II" 0 -1 1753967485750 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Quartus II" 0 -1 1753967485750 ""}  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 3 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { SYS_RST~input } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 2199 9662 10382 0}  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753967485750 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_START_REGPACKING_INFO" "" "Starting register packing" {  } {  } 0 176233 "Starting register packing" 0 0 "Fitter" 0 -1 1753967485928 ""}
{ "Extra Info" "IFSAC_FSAC_START_REG_LOCATION_PROCESSING" "" "Performing register packing on registers with non-logic cell location assignments" {  } {  } 1 176273 "Performing register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1753967485928 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_REG_LOCATION_PROCESSING" "" "Completed register packing on registers with non-logic cell location assignments" {  } {  } 1 176274 "Completed register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1753967485928 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_BEGIN_FAST_REGISTER_INFO" "" "Started Fast Input/Output/OE register processing" {  } {  } 1 176236 "Started Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1753967485929 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_FAST_REGISTER_INFO" "" "Finished Fast Input/Output/OE register processing" {  } {  } 1 176237 "Finished Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1753967485930 ""}
{ "Extra Info" "IFSAC_FSAC_START_MAC_SCAN_CHAIN_INFERENCING" "" "Start inferring scan chains for DSP blocks" {  } {  } 1 176238 "Start inferring scan chains for DSP blocks" 1 0 "Fitter" 0 -1 1753967485930 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_MAC_SCAN_CHAIN_INFERENCING" "" "Inferring scan chains for DSP blocks is complete" {  } {  } 1 176239 "Inferring scan chains for DSP blocks is complete" 1 0 "Fitter" 0 -1 1753967485930 ""}
{ "Extra Info" "IFSAC_FSAC_START_IO_MULT_RAM_PACKING" "" "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" {  } {  } 1 176248 "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" 1 0 "Fitter" 0 -1 ************* ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_IO_MULT_RAM_PACKING" "" "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" {  } {  } 1 176249 "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" 1 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_REGPACKING_INFO" "" "Finished register packing" { { "Extra Info" "IFSAC_NO_REGISTERS_WERE_PACKED" "" "No registers were packed into other blocks" {  } {  } 1 176219 "No registers were packed into other blocks" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 176235 "Finished register packing" 0 0 "Fitter" 0 -1 ************* ""}
{ "Warning" "WCUT_PLL_CLK_FEEDS_NON_DEDICATED_IO" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1 clk\[0\] DAC_CLK~output " "PLL \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1\" output port clk\[0\] feeds output pin \"DAC_CLK~output\" via non-dedicated routing -- jitter performance depends on switching rate of other design elements. Use PLL dedicated clock outputs to ensure jitter performance" {  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 46 -1 0 } } { "altpll.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altpll.tdf" 897 0 0 } } { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 30 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 6 0 0 } }  } 0 15064 "PLL \"%1!s!\" output port %2!s! feeds output pin \"%3!s!\" via non-dedicated routing -- jitter performance depends on switching rate of other design elements. Use PLL dedicated clock outputs to ensure jitter performance" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITCC_FITTER_PREPARATION_END" "00:00:00 " "Fitter preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 171121 "Fitter preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_START" "" "Fitter placement preparation operations beginning" {  } {  } 0 170189 "Fitter placement preparation operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_END" "00:00:00 " "Fitter placement preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 170190 "Fitter placement preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_START" "" "Fitter placement operations beginning" {  } {  } 0 170191 "Fitter placement operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_INFO_VPR_PLACEMENT_FINISH" "" "Fitter placement was successful" {  } {  } 0 170137 "Fitter placement was successful" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_END" "00:00:00 " "Fitter placement operations ending: elapsed time is 00:00:00" {  } {  } 0 170192 "Fitter placement operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_START" "" "Fitter routing operations beginning" {  } {  } 0 170193 "Fitter routing operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_PERCENT_ROUTING_RESOURCE_USAGE" "2 " "Router estimated average interconnect usage is 2% of the available device resources" { { "Info" "IFITAPI_FITAPI_VPR_PEAK_ROUTING_REGION" "7 X11_Y0 X22_Y11 " "Router estimated peak interconnect usage is 7% of the available device resources in the region that extends from location X11_Y0 to location X22_Y11" {  } { { "loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 1 { 0 "Router estimated peak interconnect usage is 7% of the available device resources in the region that extends from location X11_Y0 to location X22_Y11"} { { 11 { 0 "Router estimated peak interconnect usage is 7% of the available device resources in the region that extends from location X11_Y0 to location X22_Y11"} 11 0 12 12 }  }  }  }  } }  } 0 170196 "Router estimated peak interconnect usage is %1!d!%% of the available device resources in the region that extends from location %2!s! to location %3!s!" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 170195 "Router estimated average interconnect usage is %1!d!%% of the available device resources" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_END" "00:00:00 " "Fitter routing operations ending: elapsed time is 00:00:00" {  } {  } 0 170194 "Fitter routing operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED" "" "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." { { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_ROUTABILITY" "" "Optimizations that may affect the design's routability were skipped" {  } {  } 0 170201 "Optimizations that may affect the design's routability were skipped" 0 0 "Quartus II" 0 -1 ************* ""} { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_TIMING" "" "Optimizations that may affect the design's timing were skipped" {  } {  } 0 170200 "Optimizations that may affect the design's timing were skipped" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 170199 "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IVPR20K_VPR_TIMING_ANALYSIS_TIME" "0.11 " "Total time spent on timing analysis during the Fitter is 0.11 seconds." {  } {  } 0 11888 "Total time spent on timing analysis during the Fitter is %1!s! seconds." 0 0 "Fitter" 0 -1 1753967487227 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 1753967487259 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 1753967487409 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 1753967487437 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 1753967487616 ""}
{ "Info" "IFITCC_FITTER_POST_OPERATION_END" "00:00:00 " "Fitter post-fit operations ending: elapsed time is 00:00:00" {  } {  } 0 11218 "Fitter post-fit operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1753967487844 ""}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/output_files/DAC904.fit.smsg " "Generated suppressed messages file C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/output_files/DAC904.fit.smsg" {  } {  } 0 144001 "Generated suppressed messages file %1!s!" 0 0 "Fitter" 0 -1 1753967488040 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Fitter 0 s 6 s Quartus II 64-Bit " "Quartus II 64-Bit Fitter was successful. 0 errors, 6 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4977 " "Peak virtual memory: 4977 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753967488276 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 21:11:28 2025 " "Processing ended: Thu Jul 31 21:11:28 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753967488276 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:03 " "Elapsed time: 00:00:03" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753967488276 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:03 " "Total CPU time (on all processors): 00:00:03" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753967488276 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Fitter" 0 -1 1753967488276 ""}
