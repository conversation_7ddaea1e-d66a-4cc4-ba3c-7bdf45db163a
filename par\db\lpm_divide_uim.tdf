--lpm_divide DEVICE_FAMILY="Cyclone IV E" LPM_DREPRESENTATION="UNSIGNED" LPM_NREPRESENTATION="UNSIGNED" LPM_WIDTHD=2 LPM_WIDTHN=14 OPTIMIZE_FOR_SPEED=5 denom numer quotient CARRY_CHAIN="MANUAL" CARRY_CHAIN_LENGTH=48 IGNORE_CARRY_BUFFERS="OFF"
--VERSION_BEGIN 13.1 cbx_cycloneii 2013:10:23:18:05:48:SJ cbx_lpm_abs 2013:10:23:18:05:48:SJ cbx_lpm_add_sub 2013:10:23:18:05:48:SJ cbx_lpm_divide 2013:10:23:18:05:48:SJ cbx_mgl 2013:10:23:18:06:54:SJ cbx_stratix 2013:10:23:18:05:48:SJ cbx_stratixii 2013:10:23:18:05:48:SJ cbx_util_mgl 2013:10:23:18:05:48:SJ  VERSION_END


-- Copyright (C) 1991-2013 Altera Corporation
--  Your use of Altera Corporation's design tools, logic functions 
--  and other software and tools, and its AMPP partner logic 
--  functions, and any output files from any of the foregoing 
--  (including device programming or simulation files), and any 
--  associated documentation or information are expressly subject 
--  to the terms and conditions of the Altera Program License 
--  Subscription Agreement, Altera MegaCore Function License 
--  Agreement, or other applicable license agreement, including, 
--  without limitation, that your use is for the sole purpose of 
--  programming logic devices manufactured by Altera and sold by 
--  Altera or its authorized distributors.  Please refer to the 
--  applicable agreement for further details.


FUNCTION sign_div_unsign_mlh (denominator[1..0], numerator[13..0])
RETURNS ( quotient[13..0], remainder[1..0]);

--synthesis_resources = lut 48 
SUBDESIGN lpm_divide_uim
( 
	denom[1..0]	:	input;
	numer[13..0]	:	input;
	quotient[13..0]	:	output;
	remain[1..0]	:	output;
) 
VARIABLE 
	divider : sign_div_unsign_mlh;
	numer_tmp[13..0]	: WIRE;

BEGIN 
	divider.denominator[] = denom[];
	divider.numerator[] = numer_tmp[];
	numer_tmp[] = numer[];
	quotient[] = divider.quotient[];
	remain[] = divider.remainder[];
END;
--VALID FILE
