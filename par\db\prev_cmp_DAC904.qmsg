{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1753967452063 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus II 64-Bit " "Running Quartus II 64-Bit Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753967452064 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 21:10:52 2025 " "Processing started: Thu Jul 31 21:10:52 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753967452064 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Quartus II" 0 -1 1753967452064 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Quartus II" 0 -1 1753967452064 ""}
{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Quartus II" 0 -1 1753967452177 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v" { { "Info" "ISGN_ENTITY_NAME" "1 ROM_Tri " "Found entity 1: ROM_Tri" {  } { { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753967452196 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753967452196 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22/rtl/sel_wave.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22/rtl/sel_wave.v" { { "Info" "ISGN_ENTITY_NAME" "1 sel_wave " "Found entity 1: sel_wave" {  } { { "../rtl/sel_wave.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/sel_wave.v" 2 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753967452197 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753967452197 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22/rtl/key_delay.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22/rtl/key_delay.v" { { "Info" "ISGN_ENTITY_NAME" "1 key_delay " "Found entity 1: key_delay" {  } { { "../rtl/key_delay.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/key_delay.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753967452198 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753967452198 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22/rtl/key_con.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22/rtl/key_con.v" { { "Info" "ISGN_ENTITY_NAME" "1 key_con " "Found entity 1: key_con" {  } { { "../rtl/key_con.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/key_con.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753967452200 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753967452200 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22/rtl/add_32bit.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22/rtl/add_32bit.v" { { "Info" "ISGN_ENTITY_NAME" "1 add_32bit " "Found entity 1: add_32bit" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753967452201 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753967452201 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v" { { "Info" "ISGN_ENTITY_NAME" "1 ROM_Sin " "Found entity 1: ROM_Sin" {  } { { "../ip_core/ROM/ROM_Sin.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753967452202 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753967452202 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22/rtl/dac904_top.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22/rtl/dac904_top.v" { { "Info" "ISGN_ENTITY_NAME" "1 DAC904_TOP " "Found entity 1: DAC904_TOP" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753967452203 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753967452203 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v" { { "Info" "ISGN_ENTITY_NAME" "1 PLL_CLK " "Found entity 1: PLL_CLK" {  } { { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753967452204 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753967452204 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "DAC904_TOP " "Elaborating entity \"DAC904_TOP\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Quartus II" 0 -1 1753967452234 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "PLL_CLK PLL_CLK:u_PLL_CLK " "Elaborating entity \"PLL_CLK\" for hierarchy \"PLL_CLK:u_PLL_CLK\"" {  } { { "../rtl/DAC904_TOP.v" "u_PLL_CLK" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 30 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452241 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altpll PLL_CLK:u_PLL_CLK\|altpll:altpll_component " "Elaborating entity \"altpll\" for hierarchy \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\"" {  } { { "../ip_core/PLL/PLL_CLK.v" "altpll_component" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452256 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component " "Elaborated megafunction instantiation \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\"" {  } { { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753967452258 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component " "Instantiated megafunction \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "bandwidth_type AUTO " "Parameter \"bandwidth_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_divide_by 5 " "Parameter \"clk0_divide_by\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_duty_cycle 50 " "Parameter \"clk0_duty_cycle\" = \"50\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_multiply_by 2 " "Parameter \"clk0_multiply_by\" = \"2\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_phase_shift 0 " "Parameter \"clk0_phase_shift\" = \"0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "compensate_clock CLK0 " "Parameter \"compensate_clock\" = \"CLK0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "inclk0_input_frequency 20000 " "Parameter \"inclk0_input_frequency\" = \"20000\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint CBX_MODULE_PREFIX=PLL_CLK " "Parameter \"lpm_hint\" = \"CBX_MODULE_PREFIX=PLL_CLK\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altpll " "Parameter \"lpm_type\" = \"altpll\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode NORMAL " "Parameter \"operation_mode\" = \"NORMAL\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "pll_type AUTO " "Parameter \"pll_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_activeclock PORT_UNUSED " "Parameter \"port_activeclock\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_areset PORT_USED " "Parameter \"port_areset\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad0 PORT_UNUSED " "Parameter \"port_clkbad0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad1 PORT_UNUSED " "Parameter \"port_clkbad1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkloss PORT_UNUSED " "Parameter \"port_clkloss\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkswitch PORT_UNUSED " "Parameter \"port_clkswitch\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_configupdate PORT_UNUSED " "Parameter \"port_configupdate\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_fbin PORT_UNUSED " "Parameter \"port_fbin\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk0 PORT_USED " "Parameter \"port_inclk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk1 PORT_UNUSED " "Parameter \"port_inclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_locked PORT_UNUSED " "Parameter \"port_locked\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pfdena PORT_UNUSED " "Parameter \"port_pfdena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasecounterselect PORT_UNUSED " "Parameter \"port_phasecounterselect\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasedone PORT_UNUSED " "Parameter \"port_phasedone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasestep PORT_UNUSED " "Parameter \"port_phasestep\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phaseupdown PORT_UNUSED " "Parameter \"port_phaseupdown\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pllena PORT_UNUSED " "Parameter \"port_pllena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanaclr PORT_UNUSED " "Parameter \"port_scanaclr\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclk PORT_UNUSED " "Parameter \"port_scanclk\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclkena PORT_UNUSED " "Parameter \"port_scanclkena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandata PORT_UNUSED " "Parameter \"port_scandata\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandataout PORT_UNUSED " "Parameter \"port_scandataout\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandone PORT_UNUSED " "Parameter \"port_scandone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanread PORT_UNUSED " "Parameter \"port_scanread\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanwrite PORT_UNUSED " "Parameter \"port_scanwrite\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk0 PORT_USED " "Parameter \"port_clk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk1 PORT_UNUSED " "Parameter \"port_clk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk2 PORT_UNUSED " "Parameter \"port_clk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk3 PORT_UNUSED " "Parameter \"port_clk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk4 PORT_UNUSED " "Parameter \"port_clk4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk5 PORT_UNUSED " "Parameter \"port_clk5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena0 PORT_UNUSED " "Parameter \"port_clkena0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena1 PORT_UNUSED " "Parameter \"port_clkena1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena2 PORT_UNUSED " "Parameter \"port_clkena2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena3 PORT_UNUSED " "Parameter \"port_clkena3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena4 PORT_UNUSED " "Parameter \"port_clkena4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena5 PORT_UNUSED " "Parameter \"port_clkena5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk0 PORT_UNUSED " "Parameter \"port_extclk0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk1 PORT_UNUSED " "Parameter \"port_extclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk2 PORT_UNUSED " "Parameter \"port_extclk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk3 PORT_UNUSED " "Parameter \"port_extclk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_clock 5 " "Parameter \"width_clock\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452259 ""}  } { { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Quartus II" 0 -1 1753967452259 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/pll_clk_altpll.v 1 1 " "Found 1 design units, including 1 entities, in source file db/pll_clk_altpll.v" { { "Info" "ISGN_ENTITY_NAME" "1 PLL_CLK_altpll " "Found entity 1: PLL_CLK_altpll" {  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 29 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753967452289 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753967452289 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "PLL_CLK_altpll PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated " "Elaborating entity \"PLL_CLK_altpll\" for hierarchy \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\"" {  } { { "altpll.tdf" "auto_generated" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altpll.tdf" 897 3 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452289 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "ROM_Sin ROM_Sin:u_ROM_Sin " "Elaborating entity \"ROM_Sin\" for hierarchy \"ROM_Sin:u_ROM_Sin\"" {  } { { "../rtl/DAC904_TOP.v" "u_ROM_Sin" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 37 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452291 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Sin.v" "altsyncram_component" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452303 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Sin.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component " "Instantiated megafunction \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file Sin_Wave.mif " "Parameter \"init_file\" = \"Sin_Wave.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 4096 " "Parameter \"numwords_a\" = \"4096\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a CLOCK0 " "Parameter \"outdata_reg_a\" = \"CLOCK0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 12 " "Parameter \"widthad_a\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452304 ""}  } { { "../ip_core/ROM/ROM_Sin.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Quartus II" 0 -1 1753967452304 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_aj91.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_aj91.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_aj91 " "Found entity 1: altsyncram_aj91" {  } { { "db/altsyncram_aj91.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/db/altsyncram_aj91.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753967452333 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753967452333 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_aj91 ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\|altsyncram_aj91:auto_generated " "Elaborating entity \"altsyncram_aj91\" for hierarchy \"ROM_Sin:u_ROM_Sin\|altsyncram:altsyncram_component\|altsyncram_aj91:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452333 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "ROM_Tri ROM_Tri:ROM_Tri " "Elaborating entity \"ROM_Tri\" for hierarchy \"ROM_Tri:ROM_Tri\"" {  } { { "../rtl/DAC904_TOP.v" "ROM_Tri" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 44 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452336 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Tri.v" "altsyncram_component" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452339 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\"" {  } { { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component " "Instantiated megafunction \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file Tri_Wave.mif " "Parameter \"init_file\" = \"Tri_Wave.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 4096 " "Parameter \"numwords_a\" = \"4096\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 12 " "Parameter \"widthad_a\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452340 ""}  } { { "../ip_core/ROM/ROM_Tri.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Quartus II" 0 -1 1753967452340 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_4aa1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_4aa1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_4aa1 " "Found entity 1: altsyncram_4aa1" {  } { { "db/altsyncram_4aa1.tdf" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Quartus II" 0 -1 1753967452368 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Quartus II" 0 -1 1753967452368 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_4aa1 ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated " "Elaborating entity \"altsyncram_4aa1\" for hierarchy \"ROM_Tri:ROM_Tri\|altsyncram:altsyncram_component\|altsyncram_4aa1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452368 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "add_32bit add_32bit:u_add_32bit " "Elaborating entity \"add_32bit\" for hierarchy \"add_32bit:u_add_32bit\"" {  } { { "../rtl/DAC904_TOP.v" "u_add_32bit" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 52 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452370 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "key_con key_con:u_key_con " "Elaborating entity \"key_con\" for hierarchy \"key_con:u_key_con\"" {  } { { "../rtl/DAC904_TOP.v" "u_key_con" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 63 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452371 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "key_delay key_con:u_key_con\|key_delay:u_key1_delay " "Elaborating entity \"key_delay\" for hierarchy \"key_con:u_key_con\|key_delay:u_key1_delay\"" {  } { { "../rtl/key_con.v" "u_key1_delay" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/key_con.v" 26 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452372 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "sel_wave sel_wave:u_sel_wave " "Elaborating entity \"sel_wave\" for hierarchy \"sel_wave:u_sel_wave\"" {  } { { "../rtl/DAC904_TOP.v" "u_sel_wave" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 74 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Quartus II" 0 -1 1753967452375 ""}
{ "Info" "IMLS_MLS_PRESET_POWER_UP" "" "Registers with preset signals will power-up high" {  } { { "../rtl/key_con.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/key_con.v" 50 -1 0 } }  } 0 13000 "Registers with preset signals will power-up high" 0 0 "Quartus II" 0 -1 1753967452741 ""}
{ "Info" "IMLS_MLS_DEV_CLRN_SETS_REGISTERS" "" "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" {  } {  } 0 13003 "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" 0 0 "Quartus II" 0 -1 1753967452741 ""}
{ "Warning" "WMLS_MLS_STUCK_PIN_HDR" "" "Output pins are stuck at VCC or GND" { { "Warning" "WMLS_MLS_STUCK_PIN" "PD GND " "Pin \"PD\" is stuck at GND" {  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 5 -1 0 } }  } 0 13410 "Pin \"%1!s!\" is stuck at %2!s!" 0 0 "Quartus II" 0 -1 1753967452867 "|DAC904_TOP|PD"}  } {  } 0 13024 "Output pins are stuck at VCC or GND" 0 0 "Quartus II" 0 -1 1753967452867 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Timing-Driven Synthesis is running" {  } {  } 0 286030 "Timing-Driven Synthesis is running" 0 0 "Quartus II" 0 -1 1753967452953 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "1 0 1 0 0 " "Adding 1 node(s), including 0 DDIO, 1 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Quartus II" 0 -1 1753967453283 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Quartus II" 0 -1 1753967453283 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "868 " "Implemented 868 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "5 " "Implemented 5 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Quartus II" 0 -1 1753967453340 ""} { "Info" "ICUT_CUT_TM_OPINS" "16 " "Implemented 16 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Quartus II" 0 -1 1753967453340 ""} { "Info" "ICUT_CUT_TM_LCELLS" "818 " "Implemented 818 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Quartus II" 0 -1 1753967453340 ""} { "Info" "ICUT_CUT_TM_RAMS" "28 " "Implemented 28 RAM segments" {  } {  } 0 21064 "Implemented %1!d! RAM segments" 0 0 "Quartus II" 0 -1 1753967453340 ""} { "Info" "ICUT_CUT_TM_PLLS" "1 " "Implemented 1 PLLs" {  } {  } 0 21065 "Implemented %1!d! PLLs" 0 0 "Quartus II" 0 -1 1753967453340 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Quartus II" 0 -1 1753967453340 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 3 s Quartus II 64-Bit " "Quartus II 64-Bit Analysis & Synthesis was successful. 0 errors, 3 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4644 " "Peak virtual memory: 4644 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753967453350 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 21:10:53 2025 " "Processing ended: Thu Jul 31 21:10:53 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753967453350 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753967453350 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753967453350 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1753967453350 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1753967454301 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Fitter Quartus II 64-Bit " "Running Quartus II 64-Bit Fitter" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753967454301 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 21:10:54 2025 " "Processing started: Thu Jul 31 21:10:54 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753967454301 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Fitter" 0 -1 1753967454301 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_fit --read_settings_files=off --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_fit --read_settings_files=off --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Fitter" 0 -1 1753967454301 ""}
{ "Info" "0" "" "qfit2_default_script.tcl version: #1" {  } {  } 0 0 "qfit2_default_script.tcl version: #1" 0 0 "Fitter" 0 0 1753967454326 ""}
{ "Info" "0" "" "Project  = DAC904" {  } {  } 0 0 "Project  = DAC904" 0 0 "Fitter" 0 0 1753967454326 ""}
{ "Info" "0" "" "Revision = DAC904" {  } {  } 0 0 "Revision = DAC904" 0 0 "Fitter" 0 0 1753967454326 ""}
{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Fitter" 0 -1 1753967454363 ""}
{ "Info" "IMPP_MPP_USER_DEVICE" "DAC904 EP4CE6E22C8 " "Selected device EP4CE6E22C8 for design \"DAC904\"" {  } {  } 0 119006 "Selected device %2!s! for design \"%1!s!\"" 0 0 "Fitter" 0 -1 1753967454369 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1753967454392 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1753967454392 ""}
{ "Info" "ICUT_CUT_PLL_COMPUTATION_SUCCESS" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1 Cyclone IV E PLL " "Implemented PLL \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1\" as Cyclone IV E PLL type" { { "Info" "ICUT_CUT_YGR_PLL_PARAMETERS_FACTORS" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] 2 5 0 0 " "Implementing clock multiplication of 2, clock division of 5, and phase shift of 0 degrees (0 ps) for PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] port" {  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 46 -1 0 } } { "" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 276 9662 10382 0}  }  } }  } 0 15099 "Implementing clock multiplication of %2!d!, clock division of %3!d!, and phase shift of %4!d! degrees (%5!d! ps) for %1!s! port" 0 0 "Quartus II" 0 -1 1753967454421 ""}  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 46 -1 0 } } { "" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 276 9662 10382 0}  }  } }  } 0 15535 "Implemented %3!s! \"%1!s!\" as %2!s! PLL type" 0 0 "Fitter" 0 -1 1753967454421 ""}
{ "Info" "IFITCC_FITCC_INFO_AUTO_FIT_COMPILATION_ON" "" "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" {  } {  } 0 171003 "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" 0 0 "Fitter" 0 -1 1753967454448 ""}
{ "Warning" "WCPT_FEATURE_DISABLED_POST" "LogicLock " "Feature LogicLock is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." {  } {  } 0 292013 "Feature %1!s! is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." 0 0 "Fitter" 0 -1 1753967454452 ""}
{ "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED" "" "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" { { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE10E22C8 " "Device EP4CE10E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Quartus II" 0 -1 1753967454529 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE15E22C8 " "Device EP4CE15E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Quartus II" 0 -1 1753967454529 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE22E22C8 " "Device EP4CE22E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Quartus II" 0 -1 1753967454529 ""}  } {  } 2 176444 "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" 0 0 "Fitter" 0 -1 1753967454529 ""}
{ "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION" "4 " "Fitter converted 4 user pins into dedicated programming pins" { { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_ASDO_DATA1~ 6 " "Pin ~ALTERA_ASDO_DATA1~ is reserved at location 6" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_ASDO_DATA1~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_ASDO_DATA1~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 2211 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753967454530 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_FLASH_nCE_nCSO~ 8 " "Pin ~ALTERA_FLASH_nCE_nCSO~ is reserved at location 8" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_FLASH_nCE_nCSO~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_FLASH_nCE_nCSO~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 2213 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753967454530 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DCLK~ 12 " "Pin ~ALTERA_DCLK~ is reserved at location 12" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DCLK~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_DCLK~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 2215 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753967454530 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DATA0~ 13 " "Pin ~ALTERA_DATA0~ is reserved at location 13" {  } { { "g:/altera/13.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/13.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DATA0~ } } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { ~ALTERA_DATA0~ } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 2217 9662 10382 0}  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Quartus II" 0 -1 1753967454530 ""}  } {  } 0 169124 "Fitter converted %1!d! user pins into dedicated programming pins" 0 0 "Fitter" 0 -1 1753967454530 ""}
{ "Warning" "WCUT_CUT_ATOM_PINS_WITH_INCOMPLETE_IO_ASSIGNMENTS" "" "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" {  } {  } 0 15714 "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" 0 0 "Fitter" 0 -1 1753967454531 ""}
{ "Info" "IFSAC_FSAC_RAM_METASTABILITY_INFO" "" "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." {  } {  } 0 176045 "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." 0 0 "Fitter" 0 -1 1753967454532 ""}
{ "Info" "ISTA_SDC_FOUND" "../doc/SDC1.sdc " "Reading SDC File: '../doc/SDC1.sdc'" {  } {  } 0 332104 "Reading SDC File: '%1!s!'" 0 0 "Fitter" 0 -1 1753967454815 ""}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL clocks " "Deriving PLL clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} " "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\}" {  } {  } 0 332110 "%1!s!" 0 0 "Quartus II" 0 -1 1753967454817 ""}  } {  } 0 332110 "%1!s!" 0 0 "Fitter" 0 -1 1753967454817 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_CALL_IS_DELAYED" "" "Clock uncertainty is not calculated until you update the timing netlist." {  } {  } 0 332151 "Clock uncertainty is not calculated until you update the timing netlist." 0 0 "Fitter" 0 -1 1753967454817 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "key_con:u_key_con\|key_delay:u_key2_delay\|kout " "Node: key_con:u_key_con\|key_delay:u_key2_delay\|kout was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1753967454818 "|DAC904_TOP|key_con:u_key_con|key_delay:u_key2_delay|kout"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "key_con:u_key_con\|key_delay:u_key1_delay\|kout " "Node: key_con:u_key_con\|key_delay:u_key1_delay\|kout was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Fitter" 0 -1 1753967454818 "|DAC904_TOP|key_con:u_key_con|key_delay:u_key1_delay|kout"}
{ "Info" "ISTA_NO_UNCERTAINTY_FOUND" "" "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." {  } {  } 0 332154 "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." 0 0 "Fitter" 0 -1 1753967454820 ""}
{ "Info" "ISTA_USER_TDC_OPTIMIZATION_GOALS" "" "Detected timing requirements -- optimizing circuit to achieve only the specified requirements" {  } {  } 0 332129 "Detected timing requirements -- optimizing circuit to achieve only the specified requirements" 0 0 "Fitter" 0 -1 1753967454820 ""}
{ "Info" "ISTA_REPORT_CLOCKS_INFO" "Found 3 clocks " "Found 3 clocks" { { "Info" "ISTA_REPORT_CLOCKS_INFO" "  Period   Clock Name " "  Period   Clock Name" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753967454820 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "======== ============ " "======== ============" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753967454820 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "  20.000      CLK_50M " "  20.000      CLK_50M" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753967454820 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "   6.060     CLK_165M " "   6.060     CLK_165M" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753967454820 ""} { "Info" "ISTA_REPORT_CLOCKS_INFO" "  50.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " "  50.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]" {  } {  } 0 332111 "%1!s!" 0 0 "Quartus II" 0 -1 1753967454820 ""}  } {  } 0 332111 "%1!s!" 0 0 "Fitter" 0 -1 1753967454820 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_2) " "Automatically promoted node PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_2)" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G8 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G8" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""}  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 80 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { PLL_CLK:u_PLL_CLK|altpll:altpll_component|PLL_CLK_altpll:auto_generated|wire_pll1_clk[0] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 276 9662 10382 0}  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753967454840 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "key_con:u_key_con\|freq_sel  " "Automatically promoted node key_con:u_key_con\|freq_sel " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""}  } { { "../rtl/key_con.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/key_con.v" 19 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { key_con:u_key_con|freq_sel } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 164 9662 10382 0}  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753967454840 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "key_con:u_key_con\|key_delay:u_key2_delay\|kout  " "Automatically promoted node key_con:u_key_con\|key_delay:u_key2_delay\|kout " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "key_con:u_key_con\|key_delay:u_key2_delay\|kout~5 " "Destination node key_con:u_key_con\|key_delay:u_key2_delay\|kout~5" {  } { { "../rtl/key_delay.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/key_delay.v" 32 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { key_con:u_key_con|key_delay:u_key2_delay|kout~5 } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 1357 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Quartus II" 0 -1 1753967454840 ""}  } { { "../rtl/key_delay.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/key_delay.v" 32 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { key_con:u_key_con|key_delay:u_key2_delay|kout } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 372 9662 10382 0}  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753967454840 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "SYS_RST~input (placed in PIN 24 (CLK2, DIFFCLK_1p)) " "Automatically promoted node SYS_RST~input (placed in PIN 24 (CLK2, DIFFCLK_1p))" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G1 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G1" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[31\] " "Destination node add_32bit:u_add_32bit\|add\[31\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[31] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 214 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[20\] " "Destination node add_32bit:u_add_32bit\|add\[20\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[20] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 203 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[21\] " "Destination node add_32bit:u_add_32bit\|add\[21\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[21] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 204 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[22\] " "Destination node add_32bit:u_add_32bit\|add\[22\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[22] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 205 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[23\] " "Destination node add_32bit:u_add_32bit\|add\[23\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[23] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 206 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[24\] " "Destination node add_32bit:u_add_32bit\|add\[24\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[24] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 207 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[25\] " "Destination node add_32bit:u_add_32bit\|add\[25\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[25] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 208 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[26\] " "Destination node add_32bit:u_add_32bit\|add\[26\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[26] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 209 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[27\] " "Destination node add_32bit:u_add_32bit\|add\[27\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[27] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 210 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "add_32bit:u_add_32bit\|add\[28\] " "Destination node add_32bit:u_add_32bit\|add\[28\]" {  } { { "../rtl/add_32bit.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/add_32bit.v" 10 -1 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { add_32bit:u_add_32bit|add[28] } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 211 9662 10382 0}  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Quartus II" 0 -1 1753967454840 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_LIMITED_TO_SUB" "10 " "Non-global destination nodes limited to 10 nodes" {  } {  } 0 176358 "Non-global destination nodes limited to %1!d! nodes" 0 0 "Quartus II" 0 -1 1753967454840 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Quartus II" 0 -1 1753967454840 ""}  } { { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 3 0 0 } } { "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" { Floorplan "g:/altera/13.1/quartus/bin64/TimingClosureFloorplan.fld" "" "" { SYS_RST~input } "NODE_NAME" } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 0 { 0 ""} 0 2199 9662 10382 0}  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1753967454840 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_START_REGPACKING_INFO" "" "Starting register packing" {  } {  } 0 176233 "Starting register packing" 0 0 "Fitter" 0 -1 1753967455015 ""}
{ "Extra Info" "IFSAC_FSAC_START_REG_LOCATION_PROCESSING" "" "Performing register packing on registers with non-logic cell location assignments" {  } {  } 1 176273 "Performing register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1753967455016 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_REG_LOCATION_PROCESSING" "" "Completed register packing on registers with non-logic cell location assignments" {  } {  } 1 176274 "Completed register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1753967455016 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_BEGIN_FAST_REGISTER_INFO" "" "Started Fast Input/Output/OE register processing" {  } {  } 1 176236 "Started Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1753967455017 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_FAST_REGISTER_INFO" "" "Finished Fast Input/Output/OE register processing" {  } {  } 1 176237 "Finished Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1753967455017 ""}
{ "Extra Info" "IFSAC_FSAC_START_MAC_SCAN_CHAIN_INFERENCING" "" "Start inferring scan chains for DSP blocks" {  } {  } 1 176238 "Start inferring scan chains for DSP blocks" 1 0 "Fitter" 0 -1 1753967455018 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_MAC_SCAN_CHAIN_INFERENCING" "" "Inferring scan chains for DSP blocks is complete" {  } {  } 1 176239 "Inferring scan chains for DSP blocks is complete" 1 0 "Fitter" 0 -1 1753967455018 ""}
{ "Extra Info" "IFSAC_FSAC_START_IO_MULT_RAM_PACKING" "" "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" {  } {  } 1 176248 "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" 1 0 "Fitter" 0 -1 1753967455018 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_IO_MULT_RAM_PACKING" "" "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" {  } {  } 1 176249 "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" 1 0 "Fitter" 0 -1 1753967455018 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_REGPACKING_INFO" "" "Finished register packing" { { "Extra Info" "IFSAC_NO_REGISTERS_WERE_PACKED" "" "No registers were packed into other blocks" {  } {  } 1 176219 "No registers were packed into other blocks" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 176235 "Finished register packing" 0 0 "Fitter" 0 -1 ************* ""}
{ "Warning" "WCUT_PLL_CLK_FEEDS_NON_DEDICATED_IO" "PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1 clk\[0\] DAC_CLK~output " "PLL \"PLL_CLK:u_PLL_CLK\|altpll:altpll_component\|PLL_CLK_altpll:auto_generated\|pll1\" output port clk\[0\] feeds output pin \"DAC_CLK~output\" via non-dedicated routing -- jitter performance depends on switching rate of other design elements. Use PLL dedicated clock outputs to ensure jitter performance" {  } { { "db/pll_clk_altpll.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v" 46 -1 0 } } { "altpll.tdf" "" { Text "g:/altera/13.1/quartus/libraries/megafunctions/altpll.tdf" 897 0 0 } } { "../ip_core/PLL/PLL_CLK.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v" 99 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 30 0 0 } } { "../rtl/DAC904_TOP.v" "" { Text "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v" 6 0 0 } }  } 0 15064 "PLL \"%1!s!\" output port %2!s! feeds output pin \"%3!s!\" via non-dedicated routing -- jitter performance depends on switching rate of other design elements. Use PLL dedicated clock outputs to ensure jitter performance" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITCC_FITTER_PREPARATION_END" "00:00:01 " "Fitter preparation operations ending: elapsed time is 00:00:01" {  } {  } 0 171121 "Fitter preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_START" "" "Fitter placement preparation operations beginning" {  } {  } 0 170189 "Fitter placement preparation operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_END" "00:00:00 " "Fitter placement preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 170190 "Fitter placement preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_START" "" "Fitter placement operations beginning" {  } {  } 0 170191 "Fitter placement operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_INFO_VPR_PLACEMENT_FINISH" "" "Fitter placement was successful" {  } {  } 0 170137 "Fitter placement was successful" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_END" "00:00:00 " "Fitter placement operations ending: elapsed time is 00:00:00" {  } {  } 0 170192 "Fitter placement operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_START" "" "Fitter routing operations beginning" {  } {  } 0 170193 "Fitter routing operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_PERCENT_ROUTING_RESOURCE_USAGE" "2 " "Router estimated average interconnect usage is 2% of the available device resources" { { "Info" "IFITAPI_FITAPI_VPR_PEAK_ROUTING_REGION" "7 X11_Y0 X22_Y11 " "Router estimated peak interconnect usage is 7% of the available device resources in the region that extends from location X11_Y0 to location X22_Y11" {  } { { "loc" "" { Generic "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/" { { 1 { 0 "Router estimated peak interconnect usage is 7% of the available device resources in the region that extends from location X11_Y0 to location X22_Y11"} { { 11 { 0 "Router estimated peak interconnect usage is 7% of the available device resources in the region that extends from location X11_Y0 to location X22_Y11"} 11 0 12 12 }  }  }  }  } }  } 0 170196 "Router estimated peak interconnect usage is %1!d!%% of the available device resources in the region that extends from location %2!s! to location %3!s!" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 170195 "Router estimated average interconnect usage is %1!d!%% of the available device resources" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_END" "00:00:00 " "Fitter routing operations ending: elapsed time is 00:00:00" {  } {  } 0 170194 "Fitter routing operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED" "" "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." { { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_ROUTABILITY" "" "Optimizations that may affect the design's routability were skipped" {  } {  } 0 170201 "Optimizations that may affect the design's routability were skipped" 0 0 "Quartus II" 0 -1 ************* ""} { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_TIMING" "" "Optimizations that may affect the design's timing were skipped" {  } {  } 0 170200 "Optimizations that may affect the design's timing were skipped" 0 0 "Quartus II" 0 -1 ************* ""}  } {  } 0 170199 "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IVPR20K_VPR_TIMING_ANALYSIS_TIME" "0.14 " "Total time spent on timing analysis during the Fitter is 0.14 seconds." {  } {  } 0 11888 "Total time spent on timing analysis during the Fitter is %1!s! seconds." 0 0 "Fitter" 0 -1 1753967456314 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 1753967456346 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 1753967456493 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 1753967456520 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 1753967456699 ""}
{ "Info" "IFITCC_FITTER_POST_OPERATION_END" "00:00:00 " "Fitter post-fit operations ending: elapsed time is 00:00:00" {  } {  } 0 11218 "Fitter post-fit operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1753967456908 ""}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/output_files/DAC904.fit.smsg " "Generated suppressed messages file C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/output_files/DAC904.fit.smsg" {  } {  } 0 144001 "Generated suppressed messages file %1!s!" 0 0 "Fitter" 0 -1 1753967457106 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Fitter 0 s 6 s Quartus II 64-Bit " "Quartus II 64-Bit Fitter was successful. 0 errors, 6 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4977 " "Peak virtual memory: 4977 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753967457345 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 21:10:57 2025 " "Processing ended: Thu Jul 31 21:10:57 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753967457345 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:03 " "Elapsed time: 00:00:03" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753967457345 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:03 " "Total CPU time (on all processors): 00:00:03" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753967457345 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Fitter" 0 -1 1753967457345 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Fitter" 0 -1 1753967458249 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Assembler Quartus II 64-Bit " "Running Quartus II 64-Bit Assembler" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753967458249 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 21:10:58 2025 " "Processing started: Thu Jul 31 21:10:58 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753967458249 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Assembler" 0 -1 1753967458249 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_asm --read_settings_files=off --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_asm --read_settings_files=off --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Assembler" 0 -1 1753967458249 ""}
{ "Info" "IASM_ASM_GENERATING_POWER_DATA" "" "Writing out detailed assembly data for power analysis" {  } {  } 0 115031 "Writing out detailed assembly data for power analysis" 0 0 "Assembler" 0 -1 1753967458547 ""}
{ "Info" "IASM_ASM_GENERATING_PROGRAMMING_FILES" "" "Assembler is generating device programming files" {  } {  } 0 115030 "Assembler is generating device programming files" 0 0 "Assembler" 0 -1 1753967458554 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Assembler 0 s 0 s Quartus II 64-Bit " "Quartus II 64-Bit Assembler was successful. 0 errors, 0 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4595 " "Peak virtual memory: 4595 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753967458674 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 21:10:58 2025 " "Processing ended: Thu Jul 31 21:10:58 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753967458674 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:00 " "Elapsed time: 00:00:00" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753967458674 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:00 " "Total CPU time (on all processors): 00:00:00" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753967458674 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Assembler" 0 -1 1753967458674 ""}
{ "Info" "IFLOW_DISABLED_MODULE" "PowerPlay Power Analyzer FLOW_ENABLE_POWER_ANALYZER " "Skipped module PowerPlay Power Analyzer due to the assignment FLOW_ENABLE_POWER_ANALYZER" {  } {  } 0 293026 "Skipped module %1!s! due to the assignment %2!s!" 0 0 "Assembler" 0 -1 1753967459223 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Assembler" 0 -1 1753967459620 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "TimeQuest Timing Analyzer Quartus II 64-Bit " "Running Quartus II 64-Bit TimeQuest Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459621 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 21:10:59 2025 " "Processing started: Thu Jul 31 21:10:59 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753967459621 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Quartus II" 0 -1 1753967459621 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta DAC904 -c DAC904 " "Command: quartus_sta DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Quartus II" 0 -1 1753967459621 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Quartus II" 0 0 1753967459647 ""}
{ "Warning" "WQCU_PARALLEL_NO_LICENSE" "" "Parallel compilation is not licensed and has been disabled" {  } {  } 0 20028 "Parallel compilation is not licensed and has been disabled" 0 0 "Quartus II" 0 -1 1753967459704 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Quartus II" 0 -1 1753967459729 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Quartus II" 0 -1 1753967459729 ""}
{ "Info" "ISTA_SDC_FOUND" "../doc/SDC1.sdc " "Reading SDC File: '../doc/SDC1.sdc'" {  } {  } 0 332104 "Reading SDC File: '%1!s!'" 0 0 "Quartus II" 0 -1 1753967459864 ""}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL clocks " "Deriving PLL clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} " "create_generated_clock -source \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]\}" {  } {  } 0 332110 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459866 ""}  } {  } 0 332110 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459866 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_CALL_IS_DELAYED" "" "Clock uncertainty is not calculated until you update the timing netlist." {  } {  } 0 332151 "Clock uncertainty is not calculated until you update the timing netlist." 0 0 "Quartus II" 0 -1 1753967459866 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "key_con:u_key_con\|key_delay:u_key2_delay\|kout " "Node: key_con:u_key_con\|key_delay:u_key2_delay\|kout was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753967459868 "|DAC904_TOP|key_con:u_key_con|key_delay:u_key2_delay|kout"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "key_con:u_key_con\|key_delay:u_key1_delay\|kout " "Node: key_con:u_key_con\|key_delay:u_key1_delay\|kout was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753967459868 "|DAC904_TOP|key_con:u_key_con|key_delay:u_key1_delay|kout"}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459915 ""}
{ "Info" "0" "" "Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Quartus II" 0 0 1753967459916 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "Quartus II" 0 0 1753967459921 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 42.303 " "Worst-case setup slack is 42.303" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459928 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459928 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   42.303               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   42.303               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459928 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753967459928 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.408 " "Worst-case hold slack is 0.408" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459929 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459929 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.408               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.408               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459929 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753967459929 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753967459931 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753967459932 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.616 " "Worst-case minimum pulse width slack is 1.616" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459933 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459933 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.616               0.000 CLK_165M  " "    1.616               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459933 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.934               0.000 CLK_50M  " "    9.934               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459933 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.719               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.719               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967459933 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753967459933 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "Quartus II" 0 0 1753967459962 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Quartus II" 0 -1 1753967459973 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Quartus II" 0 -1 1753967460179 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "key_con:u_key_con\|key_delay:u_key2_delay\|kout " "Node: key_con:u_key_con\|key_delay:u_key2_delay\|kout was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753967460224 "|DAC904_TOP|key_con:u_key_con|key_delay:u_key2_delay|kout"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "key_con:u_key_con\|key_delay:u_key1_delay\|kout " "Node: key_con:u_key_con\|key_delay:u_key1_delay\|kout was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753967460224 "|DAC904_TOP|key_con:u_key_con|key_delay:u_key1_delay|kout"}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460225 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 42.849 " "Worst-case setup slack is 42.849" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460230 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460230 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   42.849               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   42.849               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460230 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753967460230 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.386 " "Worst-case hold slack is 0.386" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460233 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460233 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.386               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.386               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460233 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753967460233 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753967460234 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753967460236 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 1.616 " "Worst-case minimum pulse width slack is 1.616" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460238 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460238 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.616               0.000 CLK_165M  " "    1.616               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460238 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.943               0.000 CLK_50M  " "    9.943               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460238 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.718               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.718               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460238 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753967460238 ""}
{ "Info" "0" "" "Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "Quartus II" 0 0 1753967460268 ""}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "key_con:u_key_con\|key_delay:u_key2_delay\|kout " "Node: key_con:u_key_con\|key_delay:u_key2_delay\|kout was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753967460371 "|DAC904_TOP|key_con:u_key_con|key_delay:u_key2_delay|kout"}
{ "Warning" "WSTA_NODE_FOUND_WITHOUT_CLOCK_ASSIGNMENT" "key_con:u_key_con\|key_delay:u_key1_delay\|kout " "Node: key_con:u_key_con\|key_delay:u_key1_delay\|kout was determined to be a clock but was found without an associated clock assignment." {  } {  } 0 332060 "Node: %1!s! was determined to be a clock but was found without an associated clock assignment." 0 0 "Quartus II" 0 -1 1753967460371 "|DAC904_TOP|key_con:u_key_con|key_delay:u_key1_delay|kout"}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460371 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup 46.698 " "Worst-case setup slack is 46.698" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460374 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460374 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   46.698               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   46.698               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460374 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753967460374 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.135 " "Worst-case hold slack is 0.135" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460377 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460377 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.135               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.135               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460377 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753967460377 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753967460379 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Quartus II" 0 -1 1753967460381 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width 2.060 " "Worst-case minimum pulse width slack is 2.060" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460384 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460384 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.060               0.000 CLK_165M  " "    2.060               0.000 CLK_165M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460384 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.594               0.000 CLK_50M  " "    9.594               0.000 CLK_50M " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460384 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   24.734               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   24.734               0.000 u_PLL_CLK\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Quartus II" 0 -1 1753967460384 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Quartus II" 0 -1 1753967460384 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Quartus II" 0 -1 1753967460595 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Quartus II" 0 -1 1753967460595 ""}
{ "Info" "IQEXE_ERROR_COUNT" "TimeQuest Timing Analyzer 0 s 7 s Quartus II 64-Bit " "Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 7 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4634 " "Peak virtual memory: 4634 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753967460643 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 21:11:00 2025 " "Processing ended: Thu Jul 31 21:11:00 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753967460643 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753967460643 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753967460643 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1753967460643 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Quartus II" 0 -1 1753967461594 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "EDA Netlist Writer Quartus II 64-Bit " "Running Quartus II 64-Bit EDA Netlist Writer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition " "Version 13.1.0 Build 162 10/23/2013 SJ Web Edition" {  } {  } 0 0 "%1!s!" 0 0 "Quartus II" 0 -1 1753967461594 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 31 21:11:01 2025 " "Processing started: Thu Jul 31 21:11:01 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Quartus II" 0 -1 1753967461594 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Quartus II" 0 -1 1753967461594 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_eda --read_settings_files=off --write_settings_files=off DAC904 -c DAC904 " "Command: quartus_eda --read_settings_files=off --write_settings_files=off DAC904 -c DAC904" {  } {  } 0 0 "Command: %1!s!" 0 0 "Quartus II" 0 -1 1753967461594 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_8_1200mv_85c_slow.vo C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_8_1200mv_85c_slow.vo in folder \"C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753967461825 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_8_1200mv_0c_slow.vo C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_8_1200mv_0c_slow.vo in folder \"C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753967461883 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_min_1200mv_0c_fast.vo C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_min_1200mv_0c_fast.vo in folder \"C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753967461940 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904.vo C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904.vo in folder \"C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753967461997 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_8_1200mv_85c_v_slow.sdo C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_8_1200mv_85c_v_slow.sdo in folder \"C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753967462043 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_8_1200mv_0c_v_slow.sdo C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_8_1200mv_0c_v_slow.sdo in folder \"C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753967462089 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_min_1200mv_0c_v_fast.sdo C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_min_1200mv_0c_v_fast.sdo in folder \"C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753967462136 ""}
{ "Info" "IWSC_DONE_HDL_GENERATION" "DAC904_v.sdo C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/ simulation " "Generated file DAC904_v.sdo in folder \"C:/Users/<USER>/Desktop/FPGA_Cyclone_IV_E22/par/simulation/modelsim/\" for EDA simulation tool" {  } {  } 0 204019 "Generated file %1!s! in folder \"%2!s!\" for EDA %3!s! tool" 0 0 "Quartus II" 0 -1 1753967462182 ""}
{ "Info" "IQEXE_ERROR_COUNT" "EDA Netlist Writer 0 s 0 s Quartus II 64-Bit " "Quartus II 64-Bit EDA Netlist Writer was successful. 0 errors, 0 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4565 " "Peak virtual memory: 4565 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Quartus II" 0 -1 1753967462212 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 31 21:11:02 2025 " "Processing ended: Thu Jul 31 21:11:02 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Quartus II" 0 -1 1753967462212 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Quartus II" 0 -1 1753967462212 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Quartus II" 0 -1 1753967462212 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1753967462212 ""}
{ "Info" "IFLOW_ERROR_COUNT" "Full Compilation 0 s 16 s " "Quartus II Full Compilation was successful. 0 errors, 16 warnings" {  } {  } 0 293000 "Quartus II %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Quartus II" 0 -1 1753967462768 ""}
