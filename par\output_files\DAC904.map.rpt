Analysis & Synthesis report for DAC904
Thu Jul 31 17:43:23 2025
Quartus II 64-Bit Version 13.1.0 Build 162 10/23/2013 SJ Web Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Analysis & Synthesis Summary
  3. Analysis & Synthesis Settings
  4. Parallel Compilation
  5. Analysis & Synthesis Source Files Read
  6. Analysis & Synthesis Resource Usage Summary
  7. Analysis & Synthesis Resource Utilization by Entity
  8. Analysis & Synthesis RAM Summary
  9. Analysis & Synthesis IP Cores Summary
 10. General Register Statistics
 11. Inverted Register Statistics
 12. Multiplexer Restructuring Statistics (Restructuring Performed)
 13. Source assignments for ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated
 14. Source assignments for ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated
 15. Parameter Settings for User Entity Instance: PLL_CLK:u_PLL_CLK|altpll:altpll_component
 16. Parameter Settings for User Entity Instance: ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component
 17. Parameter Settings for User Entity Instance: ROM_Tri:ROM_Tri|altsyncram:altsyncram_component
 18. Parameter Settings for Inferred Entity Instance: lpm_divide:Div0
 19. altpll Parameter Settings by Entity Instance
 20. altsyncram Parameter Settings by Entity Instance
 21. Elapsed Time Per Partition
 22. Analysis & Synthesis Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2013 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+---------------------------------------------------------------------------------+
; Analysis & Synthesis Summary                                                    ;
+------------------------------------+--------------------------------------------+
; Analysis & Synthesis Status        ; Successful - Thu Jul 31 17:43:23 2025      ;
; Quartus II 64-Bit Version          ; 13.1.0 Build 162 10/23/2013 SJ Web Edition ;
; Revision Name                      ; DAC904                                     ;
; Top-level Entity Name              ; DAC904_TOP                                 ;
; Family                             ; Cyclone IV E                               ;
; Total logic elements               ; 884                                        ;
;     Total combinational functions  ; 884                                        ;
;     Dedicated logic registers      ; 277                                        ;
; Total registers                    ; 277                                        ;
; Total pins                         ; 21                                         ;
; Total virtual pins                 ; 0                                          ;
; Total memory bits                  ; 114,688                                    ;
; Embedded Multiplier 9-bit elements ; 0                                          ;
; Total PLLs                         ; 1                                          ;
+------------------------------------+--------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Settings                                                                                        ;
+----------------------------------------------------------------------------+--------------------+--------------------+
; Option                                                                     ; Setting            ; Default Value      ;
+----------------------------------------------------------------------------+--------------------+--------------------+
; Device                                                                     ; EP4CE6E22C8        ;                    ;
; Top-level entity name                                                      ; DAC904_TOP         ; DAC904             ;
; Family name                                                                ; Cyclone IV E       ; Cyclone IV GX      ;
; Use smart compilation                                                      ; Off                ; Off                ;
; Enable parallel Assembler and TimeQuest Timing Analyzer during compilation ; On                 ; On                 ;
; Enable compact report table                                                ; Off                ; Off                ;
; Restructure Multiplexers                                                   ; Auto               ; Auto               ;
; Create Debugging Nodes for IP Cores                                        ; Off                ; Off                ;
; Preserve fewer node names                                                  ; On                 ; On                 ;
; Disable OpenCore Plus hardware evaluation                                  ; Off                ; Off                ;
; Verilog Version                                                            ; Verilog_2001       ; Verilog_2001       ;
; VHDL Version                                                               ; VHDL_1993          ; VHDL_1993          ;
; State Machine Processing                                                   ; Auto               ; Auto               ;
; Safe State Machine                                                         ; Off                ; Off                ;
; Extract Verilog State Machines                                             ; On                 ; On                 ;
; Extract VHDL State Machines                                                ; On                 ; On                 ;
; Ignore Verilog initial constructs                                          ; Off                ; Off                ;
; Iteration limit for constant Verilog loops                                 ; 5000               ; 5000               ;
; Iteration limit for non-constant Verilog loops                             ; 250                ; 250                ;
; Add Pass-Through Logic to Inferred RAMs                                    ; On                 ; On                 ;
; Infer RAMs from Raw Logic                                                  ; On                 ; On                 ;
; Parallel Synthesis                                                         ; On                 ; On                 ;
; DSP Block Balancing                                                        ; Auto               ; Auto               ;
; NOT Gate Push-Back                                                         ; On                 ; On                 ;
; Power-Up Don't Care                                                        ; On                 ; On                 ;
; Remove Redundant Logic Cells                                               ; Off                ; Off                ;
; Remove Duplicate Registers                                                 ; On                 ; On                 ;
; Ignore CARRY Buffers                                                       ; Off                ; Off                ;
; Ignore CASCADE Buffers                                                     ; Off                ; Off                ;
; Ignore GLOBAL Buffers                                                      ; Off                ; Off                ;
; Ignore ROW GLOBAL Buffers                                                  ; Off                ; Off                ;
; Ignore LCELL Buffers                                                       ; Off                ; Off                ;
; Ignore SOFT Buffers                                                        ; On                 ; On                 ;
; Limit AHDL Integers to 32 Bits                                             ; Off                ; Off                ;
; Optimization Technique                                                     ; Balanced           ; Balanced           ;
; Carry Chain Length                                                         ; 70                 ; 70                 ;
; Auto Carry Chains                                                          ; On                 ; On                 ;
; Auto Open-Drain Pins                                                       ; On                 ; On                 ;
; Perform WYSIWYG Primitive Resynthesis                                      ; Off                ; Off                ;
; Auto ROM Replacement                                                       ; On                 ; On                 ;
; Auto RAM Replacement                                                       ; On                 ; On                 ;
; Auto DSP Block Replacement                                                 ; On                 ; On                 ;
; Auto Shift Register Replacement                                            ; Auto               ; Auto               ;
; Allow Shift Register Merging across Hierarchies                            ; Auto               ; Auto               ;
; Auto Clock Enable Replacement                                              ; On                 ; On                 ;
; Strict RAM Replacement                                                     ; Off                ; Off                ;
; Allow Synchronous Control Signals                                          ; On                 ; On                 ;
; Force Use of Synchronous Clear Signals                                     ; Off                ; Off                ;
; Auto RAM Block Balancing                                                   ; On                 ; On                 ;
; Auto RAM to Logic Cell Conversion                                          ; Off                ; Off                ;
; Auto Resource Sharing                                                      ; Off                ; Off                ;
; Allow Any RAM Size For Recognition                                         ; Off                ; Off                ;
; Allow Any ROM Size For Recognition                                         ; Off                ; Off                ;
; Allow Any Shift Register Size For Recognition                              ; Off                ; Off                ;
; Use LogicLock Constraints during Resource Balancing                        ; On                 ; On                 ;
; Ignore translate_off and synthesis_off directives                          ; Off                ; Off                ;
; Timing-Driven Synthesis                                                    ; On                 ; On                 ;
; Report Parameter Settings                                                  ; On                 ; On                 ;
; Report Source Assignments                                                  ; On                 ; On                 ;
; Report Connectivity Checks                                                 ; On                 ; On                 ;
; Ignore Maximum Fan-Out Assignments                                         ; Off                ; Off                ;
; Synchronization Register Chain Length                                      ; 2                  ; 2                  ;
; PowerPlay Power Optimization                                               ; Normal compilation ; Normal compilation ;
; HDL message level                                                          ; Level2             ; Level2             ;
; Suppress Register Optimization Related Messages                            ; Off                ; Off                ;
; Number of Removed Registers Reported in Synthesis Report                   ; 5000               ; 5000               ;
; Number of Swept Nodes Reported in Synthesis Report                         ; 5000               ; 5000               ;
; Number of Inverted Registers Reported in Synthesis Report                  ; 100                ; 100                ;
; Clock MUX Protection                                                       ; On                 ; On                 ;
; Auto Gated Clock Conversion                                                ; Off                ; Off                ;
; Block Design Naming                                                        ; Auto               ; Auto               ;
; SDC constraint protection                                                  ; Off                ; Off                ;
; Synthesis Effort                                                           ; Auto               ; Auto               ;
; Shift Register Replacement - Allow Asynchronous Clear Signal               ; On                 ; On                 ;
; Pre-Mapping Resynthesis Optimization                                       ; Off                ; Off                ;
; Analysis & Synthesis Message Level                                         ; Medium             ; Medium             ;
; Disable Register Merging Across Hierarchies                                ; Auto               ; Auto               ;
; Resource Aware Inference For Block RAM                                     ; On                 ; On                 ;
; Synthesis Seed                                                             ; 1                  ; 1                  ;
+----------------------------------------------------------------------------+--------------------+--------------------+


Parallel compilation was disabled, but you have multiple processors available. Enable parallel compilation to reduce compilation time.
+-------------------------------------+
; Parallel Compilation                ;
+----------------------------+--------+
; Processors                 ; Number ;
+----------------------------+--------+
; Number detected on machine ; 20     ;
; Maximum allowed            ; 1      ;
+----------------------------+--------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Source Files Read                                                                                                                                                    ;
+----------------------------------+-----------------+------------------------------+---------------------------------------------------------------------------------------------+---------+
; File Name with User-Entered Path ; Used in Netlist ; File Type                    ; File Name with Absolute Path                                                                ; Library ;
+----------------------------------+-----------------+------------------------------+---------------------------------------------------------------------------------------------+---------+
; ../ip_core/ROM/ROM_Tri.v         ; yes             ; User Wizard-Generated File   ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v          ;         ;
; ../rtl/sel_wave.v                ; yes             ; User Verilog HDL File        ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/rtl/sel_wave.v                 ;         ;
; ../rtl/key_delay.v               ; yes             ; User Verilog HDL File        ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/rtl/key_delay.v                ;         ;
; ../rtl/key_con.v                 ; yes             ; User Verilog HDL File        ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/rtl/key_con.v                  ;         ;
; ../rtl/add_32bit.v               ; yes             ; User Verilog HDL File        ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/rtl/add_32bit.v                ;         ;
; ../ip_core/ROM/ROM_Sin.v         ; yes             ; User Wizard-Generated File   ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v          ;         ;
; ../rtl/DAC904_TOP.v              ; yes             ; User Verilog HDL File        ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/rtl/DAC904_TOP.v               ;         ;
; ../ip_core/PLL/PLL_CLK.v         ; yes             ; User Wizard-Generated File   ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v          ;         ;
; altpll.tdf                       ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/altpll.tdf                                   ;         ;
; aglobal131.inc                   ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/aglobal131.inc                               ;         ;
; stratix_pll.inc                  ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/stratix_pll.inc                              ;         ;
; stratixii_pll.inc                ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/stratixii_pll.inc                            ;         ;
; cycloneii_pll.inc                ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/cycloneii_pll.inc                            ;         ;
; db/pll_clk_altpll.v              ; yes             ; Auto-Generated Megafunction  ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/par/db/pll_clk_altpll.v        ;         ;
; altsyncram.tdf                   ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/altsyncram.tdf                               ;         ;
; stratix_ram_block.inc            ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/stratix_ram_block.inc                        ;         ;
; lpm_mux.inc                      ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/lpm_mux.inc                                  ;         ;
; lpm_decode.inc                   ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/lpm_decode.inc                               ;         ;
; a_rdenreg.inc                    ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/a_rdenreg.inc                                ;         ;
; altrom.inc                       ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/altrom.inc                                   ;         ;
; altram.inc                       ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/altram.inc                                   ;         ;
; altdpram.inc                     ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/altdpram.inc                                 ;         ;
; db/altsyncram_aj91.tdf           ; yes             ; Auto-Generated Megafunction  ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/par/db/altsyncram_aj91.tdf     ;         ;
; db/altsyncram_4aa1.tdf           ; yes             ; Auto-Generated Megafunction  ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/par/db/altsyncram_4aa1.tdf     ;         ;
; lpm_divide.tdf                   ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/lpm_divide.tdf                               ;         ;
; abs_divider.inc                  ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/abs_divider.inc                              ;         ;
; sign_div_unsign.inc              ; yes             ; Megafunction                 ; g:/altera/13.1/quartus/libraries/megafunctions/sign_div_unsign.inc                          ;         ;
; db/lpm_divide_uim.tdf            ; yes             ; Auto-Generated Megafunction  ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/par/db/lpm_divide_uim.tdf      ;         ;
; db/sign_div_unsign_mlh.tdf       ; yes             ; Auto-Generated Megafunction  ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/par/db/sign_div_unsign_mlh.tdf ;         ;
; db/alt_u_div_07f.tdf             ; yes             ; Auto-Generated Megafunction  ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/par/db/alt_u_div_07f.tdf       ;         ;
; db/add_sub_7pc.tdf               ; yes             ; Auto-Generated Megafunction  ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/par/db/add_sub_7pc.tdf         ;         ;
; db/add_sub_8pc.tdf               ; yes             ; Auto-Generated Megafunction  ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/par/db/add_sub_8pc.tdf         ;         ;
+----------------------------------+-----------------+------------------------------+---------------------------------------------------------------------------------------------+---------+


+----------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Resource Usage Summary                                                                                            ;
+---------------------------------------------+------------------------------------------------------------------------------------------+
; Resource                                    ; Usage                                                                                    ;
+---------------------------------------------+------------------------------------------------------------------------------------------+
; Estimated Total logic elements              ; 884                                                                                      ;
;                                             ;                                                                                          ;
; Total combinational functions               ; 884                                                                                      ;
; Logic element usage by number of LUT inputs ;                                                                                          ;
;     -- 4 input functions                    ; 253                                                                                      ;
;     -- 3 input functions                    ; 100                                                                                      ;
;     -- <=2 input functions                  ; 531                                                                                      ;
;                                             ;                                                                                          ;
; Logic elements by mode                      ;                                                                                          ;
;     -- normal mode                          ; 383                                                                                      ;
;     -- arithmetic mode                      ; 501                                                                                      ;
;                                             ;                                                                                          ;
; Total registers                             ; 277                                                                                      ;
;     -- Dedicated logic registers            ; 277                                                                                      ;
;     -- I/O registers                        ; 0                                                                                        ;
;                                             ;                                                                                          ;
; I/O pins                                    ; 21                                                                                       ;
; Total memory bits                           ; 114688                                                                                   ;
; Embedded Multiplier 9-bit elements          ; 0                                                                                        ;
; Total PLLs                                  ; 1                                                                                        ;
;     -- PLLs                                 ; 1                                                                                        ;
;                                             ;                                                                                          ;
; Maximum fan-out node                        ; PLL_CLK:u_PLL_CLK|altpll:altpll_component|PLL_CLK_altpll:auto_generated|wire_pll1_clk[0] ;
; Maximum fan-out                             ; 273                                                                                      ;
; Total fan-out                               ; 3622                                                                                     ;
; Average fan-out                             ; 2.94                                                                                     ;
+---------------------------------------------+------------------------------------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Resource Utilization by Entity                                                                                                                                                                                                                                                      ;
+-------------------------------------------+-------------------+--------------+-------------+--------------+---------+-----------+------+--------------+-----------------------------------------------------------------------------------------------------------------------------------+--------------+
; Compilation Hierarchy Node                ; LC Combinationals ; LC Registers ; Memory Bits ; DSP Elements ; DSP 9x9 ; DSP 18x18 ; Pins ; Virtual Pins ; Full Hierarchy Name                                                                                                               ; Library Name ;
+-------------------------------------------+-------------------+--------------+-------------+--------------+---------+-----------+------+--------------+-----------------------------------------------------------------------------------------------------------------------------------+--------------+
; |DAC904_TOP                               ; 884 (14)          ; 277 (0)      ; 114688      ; 0            ; 0       ; 0         ; 21   ; 0            ; |DAC904_TOP                                                                                                                       ; work         ;
;    |PLL_CLK:u_PLL_CLK|                    ; 0 (0)             ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|PLL_CLK:u_PLL_CLK                                                                                                     ; work         ;
;       |altpll:altpll_component|           ; 0 (0)             ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|PLL_CLK:u_PLL_CLK|altpll:altpll_component                                                                             ; work         ;
;          |PLL_CLK_altpll:auto_generated|  ; 0 (0)             ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|PLL_CLK:u_PLL_CLK|altpll:altpll_component|PLL_CLK_altpll:auto_generated                                               ; work         ;
;    |ROM_Sin:u_ROM_Sin|                    ; 0 (0)             ; 0 (0)        ; 57344       ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|ROM_Sin:u_ROM_Sin                                                                                                     ; work         ;
;       |altsyncram:altsyncram_component|   ; 0 (0)             ; 0 (0)        ; 57344       ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component                                                                     ; work         ;
;          |altsyncram_aj91:auto_generated| ; 0 (0)             ; 0 (0)        ; 57344       ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated                                      ; work         ;
;    |ROM_Tri:ROM_Tri|                      ; 0 (0)             ; 0 (0)        ; 57344       ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|ROM_Tri:ROM_Tri                                                                                                       ; work         ;
;       |altsyncram:altsyncram_component|   ; 0 (0)             ; 0 (0)        ; 57344       ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component                                                                       ; work         ;
;          |altsyncram_4aa1:auto_generated| ; 0 (0)             ; 0 (0)        ; 57344       ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated                                        ; work         ;
;    |add_32bit:u_add_32bit|                ; 32 (32)           ; 32 (32)      ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|add_32bit:u_add_32bit                                                                                                 ; work         ;
;    |key_con:u_key_con|                    ; 730 (485)         ; 231 (36)     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|key_con:u_key_con                                                                                                     ; work         ;
;       |key_delay:u_key1_delay|            ; 81 (81)           ; 65 (65)      ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|key_con:u_key_con|key_delay:u_key1_delay                                                                              ; work         ;
;       |key_delay:u_key2_delay|            ; 82 (82)           ; 65 (65)      ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|key_con:u_key_con|key_delay:u_key2_delay                                                                              ; work         ;
;       |key_delay:u_key3_delay|            ; 82 (82)           ; 65 (65)      ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|key_con:u_key_con|key_delay:u_key3_delay                                                                              ; work         ;
;    |lpm_divide:Div0|                      ; 93 (0)            ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|lpm_divide:Div0                                                                                                       ; work         ;
;       |lpm_divide_uim:auto_generated|     ; 93 (0)            ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|lpm_divide:Div0|lpm_divide_uim:auto_generated                                                                         ; work         ;
;          |sign_div_unsign_mlh:divider|    ; 93 (0)            ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|lpm_divide:Div0|lpm_divide_uim:auto_generated|sign_div_unsign_mlh:divider                                             ; work         ;
;             |alt_u_div_07f:divider|       ; 93 (92)           ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|lpm_divide:Div0|lpm_divide_uim:auto_generated|sign_div_unsign_mlh:divider|alt_u_div_07f:divider                       ; work         ;
;                |add_sub_8pc:add_sub_1|    ; 1 (1)             ; 0 (0)        ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|lpm_divide:Div0|lpm_divide_uim:auto_generated|sign_div_unsign_mlh:divider|alt_u_div_07f:divider|add_sub_8pc:add_sub_1 ; work         ;
;    |sel_wave:u_sel_wave|                  ; 15 (15)           ; 14 (14)      ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |DAC904_TOP|sel_wave:u_sel_wave                                                                                                   ; work         ;
+-------------------------------------------+-------------------+--------------+-------------+--------------+---------+-----------+------+--------------+-----------------------------------------------------------------------------------------------------------------------------------+--------------+
Note: For table entries with two numbers listed, the numbers in parentheses indicate the number of resources of the given type used by the specific entity alone. The numbers listed outside of parentheses indicate the total resources of the given type used by the specific entity and all of its sub-entities in the hierarchy.


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis RAM Summary                                                                                                                                                             ;
+---------------------------------------------------------------------------------------------+------+------+--------------+--------------+--------------+--------------+-------+--------------+
; Name                                                                                        ; Type ; Mode ; Port A Depth ; Port A Width ; Port B Depth ; Port B Width ; Size  ; MIF          ;
+---------------------------------------------------------------------------------------------+------+------+--------------+--------------+--------------+--------------+-------+--------------+
; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ALTSYNCRAM ; AUTO ; ROM  ; 4096         ; 14           ; --           ; --           ; 57344 ; Sin_Wave.mif ;
; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ALTSYNCRAM   ; AUTO ; ROM  ; 4096         ; 14           ; --           ; --           ; 57344 ; Tri_Wave.mif ;
+---------------------------------------------------------------------------------------------+------+------+--------------+--------------+--------------+--------------+-------+--------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis IP Cores Summary                                                                                                                                              ;
+--------+--------------+---------+--------------+--------------+-------------------------------+------------------------------------------------------------------------------------+
; Vendor ; IP Core Name ; Version ; Release Date ; License Type ; Entity Instance               ; IP Include File                                                                    ;
+--------+--------------+---------+--------------+--------------+-------------------------------+------------------------------------------------------------------------------------+
; Altera ; ROM: 1-PORT  ; N/A     ; N/A          ; N/A          ; |DAC904_TOP|ROM_Tri:ROM_Tri   ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Tri.v ;
; Altera ; ALTPLL       ; 13.0    ; N/A          ; N/A          ; |DAC904_TOP|PLL_CLK:u_PLL_CLK ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/ip_core/PLL/PLL_CLK.v ;
; Altera ; ROM: 1-PORT  ; N/A     ; N/A          ; N/A          ; |DAC904_TOP|ROM_Sin:u_ROM_Sin ; C:/Users/<USER>/Downloads/zuolan_v4.1/FPGA_Cyclone_IV_E22/ip_core/ROM/ROM_Sin.v ;
+--------+--------------+---------+--------------+--------------+-------------------------------+------------------------------------------------------------------------------------+


+------------------------------------------------------+
; General Register Statistics                          ;
+----------------------------------------------+-------+
; Statistic                                    ; Value ;
+----------------------------------------------+-------+
; Total registers                              ; 277   ;
; Number of registers using Synchronous Clear  ; 224   ;
; Number of registers using Synchronous Load   ; 14    ;
; Number of registers using Asynchronous Clear ; 48    ;
; Number of registers using Asynchronous Load  ; 0     ;
; Number of registers using Clock Enable       ; 32    ;
; Number of registers using Preset             ; 0     ;
+----------------------------------------------+-------+


+---------------------------------------------------+
; Inverted Register Statistics                      ;
+-----------------------------------------+---------+
; Inverted Register                       ; Fan out ;
+-----------------------------------------+---------+
; key_con:u_key_con|fre[17]               ; 12      ;
; key_con:u_key_con|fre[16]               ; 12      ;
; key_con:u_key_con|fre[14]               ; 14      ;
; key_con:u_key_con|fre[10]               ; 16      ;
; key_con:u_key_con|fre[9]                ; 13      ;
; key_con:u_key_con|fre[7]                ; 14      ;
; key_con:u_key_con|fre[6]                ; 13      ;
; key_con:u_key_con|fre[4]                ; 17      ;
; key_con:u_key_con|fre[3]                ; 16      ;
; key_con:u_key_con|fre[2]                ; 15      ;
; Total number of inverted registers = 10 ;         ;
+-----------------------------------------+---------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Multiplexer Restructuring Statistics (Restructuring Performed)                                                                                              ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+-----------------------------------------------+
; Multiplexer Inputs ; Bus Width ; Baseline Area ; Area if Restructured ; Saving if Restructured ; Registered ; Example Multiplexer Output                    ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+-----------------------------------------------+
; 4:1                ; 14 bits   ; 28 LEs        ; 28 LEs               ; 0 LEs                  ; Yes        ; |DAC904_TOP|sel_wave:u_sel_wave|da_out_reg[1] ;
; 11:1               ; 2 bits    ; 14 LEs        ; 4 LEs                ; 10 LEs                 ; Yes        ; |DAC904_TOP|key_con:u_key_con|fre[0]          ;
; 11:1               ; 20 bits   ; 140 LEs       ; 120 LEs              ; 20 LEs                 ; Yes        ; |DAC904_TOP|key_con:u_key_con|fre[5]          ;
; 11:1               ; 10 bits   ; 70 LEs        ; 50 LEs               ; 20 LEs                 ; Yes        ; |DAC904_TOP|key_con:u_key_con|fre[6]          ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+-----------------------------------------------+


+---------------------------------------------------------------------------------------------------------+
; Source assignments for ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated ;
+---------------------------------+--------------------+------+-------------------------------------------+
; Assignment                      ; Value              ; From ; To                                        ;
+---------------------------------+--------------------+------+-------------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                         ;
+---------------------------------+--------------------+------+-------------------------------------------+


+-------------------------------------------------------------------------------------------------------+
; Source assignments for ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated ;
+---------------------------------+--------------------+------+-----------------------------------------+
; Assignment                      ; Value              ; From ; To                                      ;
+---------------------------------+--------------------+------+-----------------------------------------+
; OPTIMIZE_POWER_DURING_SYNTHESIS ; NORMAL_COMPILATION ; -    ; -                                       ;
+---------------------------------+--------------------+------+-----------------------------------------+


+----------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: PLL_CLK:u_PLL_CLK|altpll:altpll_component ;
+-------------------------------+---------------------------+----------------------------+
; Parameter Name                ; Value                     ; Type                       ;
+-------------------------------+---------------------------+----------------------------+
; OPERATION_MODE                ; NORMAL                    ; Untyped                    ;
; PLL_TYPE                      ; AUTO                      ; Untyped                    ;
; LPM_HINT                      ; CBX_MODULE_PREFIX=PLL_CLK ; Untyped                    ;
; QUALIFY_CONF_DONE             ; OFF                       ; Untyped                    ;
; COMPENSATE_CLOCK              ; CLK0                      ; Untyped                    ;
; SCAN_CHAIN                    ; LONG                      ; Untyped                    ;
; PRIMARY_CLOCK                 ; INCLK0                    ; Untyped                    ;
; INCLK0_INPUT_FREQUENCY        ; 20000                     ; Signed Integer             ;
; INCLK1_INPUT_FREQUENCY        ; 0                         ; Untyped                    ;
; GATE_LOCK_SIGNAL              ; NO                        ; Untyped                    ;
; GATE_LOCK_COUNTER             ; 0                         ; Untyped                    ;
; LOCK_HIGH                     ; 1                         ; Untyped                    ;
; LOCK_LOW                      ; 1                         ; Untyped                    ;
; VALID_LOCK_MULTIPLIER         ; 1                         ; Untyped                    ;
; INVALID_LOCK_MULTIPLIER       ; 5                         ; Untyped                    ;
; SWITCH_OVER_ON_LOSSCLK        ; OFF                       ; Untyped                    ;
; SWITCH_OVER_ON_GATED_LOCK     ; OFF                       ; Untyped                    ;
; ENABLE_SWITCH_OVER_COUNTER    ; OFF                       ; Untyped                    ;
; SKIP_VCO                      ; OFF                       ; Untyped                    ;
; SWITCH_OVER_COUNTER           ; 0                         ; Untyped                    ;
; SWITCH_OVER_TYPE              ; AUTO                      ; Untyped                    ;
; FEEDBACK_SOURCE               ; EXTCLK0                   ; Untyped                    ;
; BANDWIDTH                     ; 0                         ; Untyped                    ;
; BANDWIDTH_TYPE                ; AUTO                      ; Untyped                    ;
; SPREAD_FREQUENCY              ; 0                         ; Untyped                    ;
; DOWN_SPREAD                   ; 0                         ; Untyped                    ;
; SELF_RESET_ON_GATED_LOSS_LOCK ; OFF                       ; Untyped                    ;
; SELF_RESET_ON_LOSS_LOCK       ; OFF                       ; Untyped                    ;
; CLK9_MULTIPLY_BY              ; 0                         ; Untyped                    ;
; CLK8_MULTIPLY_BY              ; 0                         ; Untyped                    ;
; CLK7_MULTIPLY_BY              ; 0                         ; Untyped                    ;
; CLK6_MULTIPLY_BY              ; 0                         ; Untyped                    ;
; CLK5_MULTIPLY_BY              ; 1                         ; Untyped                    ;
; CLK4_MULTIPLY_BY              ; 1                         ; Untyped                    ;
; CLK3_MULTIPLY_BY              ; 1                         ; Untyped                    ;
; CLK2_MULTIPLY_BY              ; 1                         ; Untyped                    ;
; CLK1_MULTIPLY_BY              ; 1                         ; Untyped                    ;
; CLK0_MULTIPLY_BY              ; 2                         ; Signed Integer             ;
; CLK9_DIVIDE_BY                ; 0                         ; Untyped                    ;
; CLK8_DIVIDE_BY                ; 0                         ; Untyped                    ;
; CLK7_DIVIDE_BY                ; 0                         ; Untyped                    ;
; CLK6_DIVIDE_BY                ; 0                         ; Untyped                    ;
; CLK5_DIVIDE_BY                ; 1                         ; Untyped                    ;
; CLK4_DIVIDE_BY                ; 1                         ; Untyped                    ;
; CLK3_DIVIDE_BY                ; 1                         ; Untyped                    ;
; CLK2_DIVIDE_BY                ; 1                         ; Untyped                    ;
; CLK1_DIVIDE_BY                ; 1                         ; Untyped                    ;
; CLK0_DIVIDE_BY                ; 5                         ; Signed Integer             ;
; CLK9_PHASE_SHIFT              ; 0                         ; Untyped                    ;
; CLK8_PHASE_SHIFT              ; 0                         ; Untyped                    ;
; CLK7_PHASE_SHIFT              ; 0                         ; Untyped                    ;
; CLK6_PHASE_SHIFT              ; 0                         ; Untyped                    ;
; CLK5_PHASE_SHIFT              ; 0                         ; Untyped                    ;
; CLK4_PHASE_SHIFT              ; 0                         ; Untyped                    ;
; CLK3_PHASE_SHIFT              ; 0                         ; Untyped                    ;
; CLK2_PHASE_SHIFT              ; 0                         ; Untyped                    ;
; CLK1_PHASE_SHIFT              ; 0                         ; Untyped                    ;
; CLK0_PHASE_SHIFT              ; 0                         ; Untyped                    ;
; CLK5_TIME_DELAY               ; 0                         ; Untyped                    ;
; CLK4_TIME_DELAY               ; 0                         ; Untyped                    ;
; CLK3_TIME_DELAY               ; 0                         ; Untyped                    ;
; CLK2_TIME_DELAY               ; 0                         ; Untyped                    ;
; CLK1_TIME_DELAY               ; 0                         ; Untyped                    ;
; CLK0_TIME_DELAY               ; 0                         ; Untyped                    ;
; CLK9_DUTY_CYCLE               ; 50                        ; Untyped                    ;
; CLK8_DUTY_CYCLE               ; 50                        ; Untyped                    ;
; CLK7_DUTY_CYCLE               ; 50                        ; Untyped                    ;
; CLK6_DUTY_CYCLE               ; 50                        ; Untyped                    ;
; CLK5_DUTY_CYCLE               ; 50                        ; Untyped                    ;
; CLK4_DUTY_CYCLE               ; 50                        ; Untyped                    ;
; CLK3_DUTY_CYCLE               ; 50                        ; Untyped                    ;
; CLK2_DUTY_CYCLE               ; 50                        ; Untyped                    ;
; CLK1_DUTY_CYCLE               ; 50                        ; Untyped                    ;
; CLK0_DUTY_CYCLE               ; 50                        ; Signed Integer             ;
; CLK9_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                    ;
; CLK8_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                    ;
; CLK7_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                    ;
; CLK6_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                    ;
; CLK5_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                    ;
; CLK4_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                    ;
; CLK3_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                    ;
; CLK2_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                    ;
; CLK1_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                    ;
; CLK0_USE_EVEN_COUNTER_MODE    ; OFF                       ; Untyped                    ;
; CLK9_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                    ;
; CLK8_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                    ;
; CLK7_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                    ;
; CLK6_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                    ;
; CLK5_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                    ;
; CLK4_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                    ;
; CLK3_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                    ;
; CLK2_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                    ;
; CLK1_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                    ;
; CLK0_USE_EVEN_COUNTER_VALUE   ; OFF                       ; Untyped                    ;
; LOCK_WINDOW_UI                ;  0.05                     ; Untyped                    ;
; LOCK_WINDOW_UI_BITS           ; UNUSED                    ; Untyped                    ;
; VCO_RANGE_DETECTOR_LOW_BITS   ; UNUSED                    ; Untyped                    ;
; VCO_RANGE_DETECTOR_HIGH_BITS  ; UNUSED                    ; Untyped                    ;
; DPA_MULTIPLY_BY               ; 0                         ; Untyped                    ;
; DPA_DIVIDE_BY                 ; 1                         ; Untyped                    ;
; DPA_DIVIDER                   ; 0                         ; Untyped                    ;
; EXTCLK3_MULTIPLY_BY           ; 1                         ; Untyped                    ;
; EXTCLK2_MULTIPLY_BY           ; 1                         ; Untyped                    ;
; EXTCLK1_MULTIPLY_BY           ; 1                         ; Untyped                    ;
; EXTCLK0_MULTIPLY_BY           ; 1                         ; Untyped                    ;
; EXTCLK3_DIVIDE_BY             ; 1                         ; Untyped                    ;
; EXTCLK2_DIVIDE_BY             ; 1                         ; Untyped                    ;
; EXTCLK1_DIVIDE_BY             ; 1                         ; Untyped                    ;
; EXTCLK0_DIVIDE_BY             ; 1                         ; Untyped                    ;
; EXTCLK3_PHASE_SHIFT           ; 0                         ; Untyped                    ;
; EXTCLK2_PHASE_SHIFT           ; 0                         ; Untyped                    ;
; EXTCLK1_PHASE_SHIFT           ; 0                         ; Untyped                    ;
; EXTCLK0_PHASE_SHIFT           ; 0                         ; Untyped                    ;
; EXTCLK3_TIME_DELAY            ; 0                         ; Untyped                    ;
; EXTCLK2_TIME_DELAY            ; 0                         ; Untyped                    ;
; EXTCLK1_TIME_DELAY            ; 0                         ; Untyped                    ;
; EXTCLK0_TIME_DELAY            ; 0                         ; Untyped                    ;
; EXTCLK3_DUTY_CYCLE            ; 50                        ; Untyped                    ;
; EXTCLK2_DUTY_CYCLE            ; 50                        ; Untyped                    ;
; EXTCLK1_DUTY_CYCLE            ; 50                        ; Untyped                    ;
; EXTCLK0_DUTY_CYCLE            ; 50                        ; Untyped                    ;
; VCO_MULTIPLY_BY               ; 0                         ; Untyped                    ;
; VCO_DIVIDE_BY                 ; 0                         ; Untyped                    ;
; SCLKOUT0_PHASE_SHIFT          ; 0                         ; Untyped                    ;
; SCLKOUT1_PHASE_SHIFT          ; 0                         ; Untyped                    ;
; VCO_MIN                       ; 0                         ; Untyped                    ;
; VCO_MAX                       ; 0                         ; Untyped                    ;
; VCO_CENTER                    ; 0                         ; Untyped                    ;
; PFD_MIN                       ; 0                         ; Untyped                    ;
; PFD_MAX                       ; 0                         ; Untyped                    ;
; M_INITIAL                     ; 0                         ; Untyped                    ;
; M                             ; 0                         ; Untyped                    ;
; N                             ; 1                         ; Untyped                    ;
; M2                            ; 1                         ; Untyped                    ;
; N2                            ; 1                         ; Untyped                    ;
; SS                            ; 1                         ; Untyped                    ;
; C0_HIGH                       ; 0                         ; Untyped                    ;
; C1_HIGH                       ; 0                         ; Untyped                    ;
; C2_HIGH                       ; 0                         ; Untyped                    ;
; C3_HIGH                       ; 0                         ; Untyped                    ;
; C4_HIGH                       ; 0                         ; Untyped                    ;
; C5_HIGH                       ; 0                         ; Untyped                    ;
; C6_HIGH                       ; 0                         ; Untyped                    ;
; C7_HIGH                       ; 0                         ; Untyped                    ;
; C8_HIGH                       ; 0                         ; Untyped                    ;
; C9_HIGH                       ; 0                         ; Untyped                    ;
; C0_LOW                        ; 0                         ; Untyped                    ;
; C1_LOW                        ; 0                         ; Untyped                    ;
; C2_LOW                        ; 0                         ; Untyped                    ;
; C3_LOW                        ; 0                         ; Untyped                    ;
; C4_LOW                        ; 0                         ; Untyped                    ;
; C5_LOW                        ; 0                         ; Untyped                    ;
; C6_LOW                        ; 0                         ; Untyped                    ;
; C7_LOW                        ; 0                         ; Untyped                    ;
; C8_LOW                        ; 0                         ; Untyped                    ;
; C9_LOW                        ; 0                         ; Untyped                    ;
; C0_INITIAL                    ; 0                         ; Untyped                    ;
; C1_INITIAL                    ; 0                         ; Untyped                    ;
; C2_INITIAL                    ; 0                         ; Untyped                    ;
; C3_INITIAL                    ; 0                         ; Untyped                    ;
; C4_INITIAL                    ; 0                         ; Untyped                    ;
; C5_INITIAL                    ; 0                         ; Untyped                    ;
; C6_INITIAL                    ; 0                         ; Untyped                    ;
; C7_INITIAL                    ; 0                         ; Untyped                    ;
; C8_INITIAL                    ; 0                         ; Untyped                    ;
; C9_INITIAL                    ; 0                         ; Untyped                    ;
; C0_MODE                       ; BYPASS                    ; Untyped                    ;
; C1_MODE                       ; BYPASS                    ; Untyped                    ;
; C2_MODE                       ; BYPASS                    ; Untyped                    ;
; C3_MODE                       ; BYPASS                    ; Untyped                    ;
; C4_MODE                       ; BYPASS                    ; Untyped                    ;
; C5_MODE                       ; BYPASS                    ; Untyped                    ;
; C6_MODE                       ; BYPASS                    ; Untyped                    ;
; C7_MODE                       ; BYPASS                    ; Untyped                    ;
; C8_MODE                       ; BYPASS                    ; Untyped                    ;
; C9_MODE                       ; BYPASS                    ; Untyped                    ;
; C0_PH                         ; 0                         ; Untyped                    ;
; C1_PH                         ; 0                         ; Untyped                    ;
; C2_PH                         ; 0                         ; Untyped                    ;
; C3_PH                         ; 0                         ; Untyped                    ;
; C4_PH                         ; 0                         ; Untyped                    ;
; C5_PH                         ; 0                         ; Untyped                    ;
; C6_PH                         ; 0                         ; Untyped                    ;
; C7_PH                         ; 0                         ; Untyped                    ;
; C8_PH                         ; 0                         ; Untyped                    ;
; C9_PH                         ; 0                         ; Untyped                    ;
; L0_HIGH                       ; 1                         ; Untyped                    ;
; L1_HIGH                       ; 1                         ; Untyped                    ;
; G0_HIGH                       ; 1                         ; Untyped                    ;
; G1_HIGH                       ; 1                         ; Untyped                    ;
; G2_HIGH                       ; 1                         ; Untyped                    ;
; G3_HIGH                       ; 1                         ; Untyped                    ;
; E0_HIGH                       ; 1                         ; Untyped                    ;
; E1_HIGH                       ; 1                         ; Untyped                    ;
; E2_HIGH                       ; 1                         ; Untyped                    ;
; E3_HIGH                       ; 1                         ; Untyped                    ;
; L0_LOW                        ; 1                         ; Untyped                    ;
; L1_LOW                        ; 1                         ; Untyped                    ;
; G0_LOW                        ; 1                         ; Untyped                    ;
; G1_LOW                        ; 1                         ; Untyped                    ;
; G2_LOW                        ; 1                         ; Untyped                    ;
; G3_LOW                        ; 1                         ; Untyped                    ;
; E0_LOW                        ; 1                         ; Untyped                    ;
; E1_LOW                        ; 1                         ; Untyped                    ;
; E2_LOW                        ; 1                         ; Untyped                    ;
; E3_LOW                        ; 1                         ; Untyped                    ;
; L0_INITIAL                    ; 1                         ; Untyped                    ;
; L1_INITIAL                    ; 1                         ; Untyped                    ;
; G0_INITIAL                    ; 1                         ; Untyped                    ;
; G1_INITIAL                    ; 1                         ; Untyped                    ;
; G2_INITIAL                    ; 1                         ; Untyped                    ;
; G3_INITIAL                    ; 1                         ; Untyped                    ;
; E0_INITIAL                    ; 1                         ; Untyped                    ;
; E1_INITIAL                    ; 1                         ; Untyped                    ;
; E2_INITIAL                    ; 1                         ; Untyped                    ;
; E3_INITIAL                    ; 1                         ; Untyped                    ;
; L0_MODE                       ; BYPASS                    ; Untyped                    ;
; L1_MODE                       ; BYPASS                    ; Untyped                    ;
; G0_MODE                       ; BYPASS                    ; Untyped                    ;
; G1_MODE                       ; BYPASS                    ; Untyped                    ;
; G2_MODE                       ; BYPASS                    ; Untyped                    ;
; G3_MODE                       ; BYPASS                    ; Untyped                    ;
; E0_MODE                       ; BYPASS                    ; Untyped                    ;
; E1_MODE                       ; BYPASS                    ; Untyped                    ;
; E2_MODE                       ; BYPASS                    ; Untyped                    ;
; E3_MODE                       ; BYPASS                    ; Untyped                    ;
; L0_PH                         ; 0                         ; Untyped                    ;
; L1_PH                         ; 0                         ; Untyped                    ;
; G0_PH                         ; 0                         ; Untyped                    ;
; G1_PH                         ; 0                         ; Untyped                    ;
; G2_PH                         ; 0                         ; Untyped                    ;
; G3_PH                         ; 0                         ; Untyped                    ;
; E0_PH                         ; 0                         ; Untyped                    ;
; E1_PH                         ; 0                         ; Untyped                    ;
; E2_PH                         ; 0                         ; Untyped                    ;
; E3_PH                         ; 0                         ; Untyped                    ;
; M_PH                          ; 0                         ; Untyped                    ;
; C1_USE_CASC_IN                ; OFF                       ; Untyped                    ;
; C2_USE_CASC_IN                ; OFF                       ; Untyped                    ;
; C3_USE_CASC_IN                ; OFF                       ; Untyped                    ;
; C4_USE_CASC_IN                ; OFF                       ; Untyped                    ;
; C5_USE_CASC_IN                ; OFF                       ; Untyped                    ;
; C6_USE_CASC_IN                ; OFF                       ; Untyped                    ;
; C7_USE_CASC_IN                ; OFF                       ; Untyped                    ;
; C8_USE_CASC_IN                ; OFF                       ; Untyped                    ;
; C9_USE_CASC_IN                ; OFF                       ; Untyped                    ;
; CLK0_COUNTER                  ; G0                        ; Untyped                    ;
; CLK1_COUNTER                  ; G0                        ; Untyped                    ;
; CLK2_COUNTER                  ; G0                        ; Untyped                    ;
; CLK3_COUNTER                  ; G0                        ; Untyped                    ;
; CLK4_COUNTER                  ; G0                        ; Untyped                    ;
; CLK5_COUNTER                  ; G0                        ; Untyped                    ;
; CLK6_COUNTER                  ; E0                        ; Untyped                    ;
; CLK7_COUNTER                  ; E1                        ; Untyped                    ;
; CLK8_COUNTER                  ; E2                        ; Untyped                    ;
; CLK9_COUNTER                  ; E3                        ; Untyped                    ;
; L0_TIME_DELAY                 ; 0                         ; Untyped                    ;
; L1_TIME_DELAY                 ; 0                         ; Untyped                    ;
; G0_TIME_DELAY                 ; 0                         ; Untyped                    ;
; G1_TIME_DELAY                 ; 0                         ; Untyped                    ;
; G2_TIME_DELAY                 ; 0                         ; Untyped                    ;
; G3_TIME_DELAY                 ; 0                         ; Untyped                    ;
; E0_TIME_DELAY                 ; 0                         ; Untyped                    ;
; E1_TIME_DELAY                 ; 0                         ; Untyped                    ;
; E2_TIME_DELAY                 ; 0                         ; Untyped                    ;
; E3_TIME_DELAY                 ; 0                         ; Untyped                    ;
; M_TIME_DELAY                  ; 0                         ; Untyped                    ;
; N_TIME_DELAY                  ; 0                         ; Untyped                    ;
; EXTCLK3_COUNTER               ; E3                        ; Untyped                    ;
; EXTCLK2_COUNTER               ; E2                        ; Untyped                    ;
; EXTCLK1_COUNTER               ; E1                        ; Untyped                    ;
; EXTCLK0_COUNTER               ; E0                        ; Untyped                    ;
; ENABLE0_COUNTER               ; L0                        ; Untyped                    ;
; ENABLE1_COUNTER               ; L0                        ; Untyped                    ;
; CHARGE_PUMP_CURRENT           ; 2                         ; Untyped                    ;
; LOOP_FILTER_R                 ;  1.000000                 ; Untyped                    ;
; LOOP_FILTER_C                 ; 5                         ; Untyped                    ;
; CHARGE_PUMP_CURRENT_BITS      ; 9999                      ; Untyped                    ;
; LOOP_FILTER_R_BITS            ; 9999                      ; Untyped                    ;
; LOOP_FILTER_C_BITS            ; 9999                      ; Untyped                    ;
; VCO_POST_SCALE                ; 0                         ; Untyped                    ;
; CLK2_OUTPUT_FREQUENCY         ; 0                         ; Untyped                    ;
; CLK1_OUTPUT_FREQUENCY         ; 0                         ; Untyped                    ;
; CLK0_OUTPUT_FREQUENCY         ; 0                         ; Untyped                    ;
; INTENDED_DEVICE_FAMILY        ; Cyclone IV E              ; Untyped                    ;
; PORT_CLKENA0                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLKENA1                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLKENA2                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLKENA3                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLKENA4                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLKENA5                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_EXTCLKENA0               ; PORT_CONNECTIVITY         ; Untyped                    ;
; PORT_EXTCLKENA1               ; PORT_CONNECTIVITY         ; Untyped                    ;
; PORT_EXTCLKENA2               ; PORT_CONNECTIVITY         ; Untyped                    ;
; PORT_EXTCLKENA3               ; PORT_CONNECTIVITY         ; Untyped                    ;
; PORT_EXTCLK0                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_EXTCLK1                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_EXTCLK2                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_EXTCLK3                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLKBAD0                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLKBAD1                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLK0                     ; PORT_USED                 ; Untyped                    ;
; PORT_CLK1                     ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLK2                     ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLK3                     ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLK4                     ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLK5                     ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLK6                     ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLK7                     ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLK8                     ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLK9                     ; PORT_UNUSED               ; Untyped                    ;
; PORT_SCANDATA                 ; PORT_UNUSED               ; Untyped                    ;
; PORT_SCANDATAOUT              ; PORT_UNUSED               ; Untyped                    ;
; PORT_SCANDONE                 ; PORT_UNUSED               ; Untyped                    ;
; PORT_SCLKOUT1                 ; PORT_CONNECTIVITY         ; Untyped                    ;
; PORT_SCLKOUT0                 ; PORT_CONNECTIVITY         ; Untyped                    ;
; PORT_ACTIVECLOCK              ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLKLOSS                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_INCLK1                   ; PORT_UNUSED               ; Untyped                    ;
; PORT_INCLK0                   ; PORT_USED                 ; Untyped                    ;
; PORT_FBIN                     ; PORT_UNUSED               ; Untyped                    ;
; PORT_PLLENA                   ; PORT_UNUSED               ; Untyped                    ;
; PORT_CLKSWITCH                ; PORT_UNUSED               ; Untyped                    ;
; PORT_ARESET                   ; PORT_USED                 ; Untyped                    ;
; PORT_PFDENA                   ; PORT_UNUSED               ; Untyped                    ;
; PORT_SCANCLK                  ; PORT_UNUSED               ; Untyped                    ;
; PORT_SCANACLR                 ; PORT_UNUSED               ; Untyped                    ;
; PORT_SCANREAD                 ; PORT_UNUSED               ; Untyped                    ;
; PORT_SCANWRITE                ; PORT_UNUSED               ; Untyped                    ;
; PORT_ENABLE0                  ; PORT_CONNECTIVITY         ; Untyped                    ;
; PORT_ENABLE1                  ; PORT_CONNECTIVITY         ; Untyped                    ;
; PORT_LOCKED                   ; PORT_UNUSED               ; Untyped                    ;
; PORT_CONFIGUPDATE             ; PORT_UNUSED               ; Untyped                    ;
; PORT_FBOUT                    ; PORT_CONNECTIVITY         ; Untyped                    ;
; PORT_PHASEDONE                ; PORT_UNUSED               ; Untyped                    ;
; PORT_PHASESTEP                ; PORT_UNUSED               ; Untyped                    ;
; PORT_PHASEUPDOWN              ; PORT_UNUSED               ; Untyped                    ;
; PORT_SCANCLKENA               ; PORT_UNUSED               ; Untyped                    ;
; PORT_PHASECOUNTERSELECT       ; PORT_UNUSED               ; Untyped                    ;
; PORT_VCOOVERRANGE             ; PORT_CONNECTIVITY         ; Untyped                    ;
; PORT_VCOUNDERRANGE            ; PORT_CONNECTIVITY         ; Untyped                    ;
; M_TEST_SOURCE                 ; 5                         ; Untyped                    ;
; C0_TEST_SOURCE                ; 5                         ; Untyped                    ;
; C1_TEST_SOURCE                ; 5                         ; Untyped                    ;
; C2_TEST_SOURCE                ; 5                         ; Untyped                    ;
; C3_TEST_SOURCE                ; 5                         ; Untyped                    ;
; C4_TEST_SOURCE                ; 5                         ; Untyped                    ;
; C5_TEST_SOURCE                ; 5                         ; Untyped                    ;
; C6_TEST_SOURCE                ; 5                         ; Untyped                    ;
; C7_TEST_SOURCE                ; 5                         ; Untyped                    ;
; C8_TEST_SOURCE                ; 5                         ; Untyped                    ;
; C9_TEST_SOURCE                ; 5                         ; Untyped                    ;
; CBXI_PARAMETER                ; PLL_CLK_altpll            ; Untyped                    ;
; VCO_FREQUENCY_CONTROL         ; AUTO                      ; Untyped                    ;
; VCO_PHASE_SHIFT_STEP          ; 0                         ; Untyped                    ;
; WIDTH_CLOCK                   ; 5                         ; Signed Integer             ;
; WIDTH_PHASECOUNTERSELECT      ; 4                         ; Untyped                    ;
; USING_FBMIMICBIDIR_PORT       ; OFF                       ; Untyped                    ;
; DEVICE_FAMILY                 ; Cyclone IV E              ; Untyped                    ;
; SCAN_CHAIN_MIF_FILE           ; UNUSED                    ; Untyped                    ;
; SIM_GATE_LOCK_DEVICE_BEHAVIOR ; OFF                       ; Untyped                    ;
; AUTO_CARRY_CHAINS             ; ON                        ; AUTO_CARRY                 ;
; IGNORE_CARRY_BUFFERS          ; OFF                       ; IGNORE_CARRY               ;
; AUTO_CASCADE_CHAINS           ; ON                        ; AUTO_CASCADE               ;
; IGNORE_CASCADE_BUFFERS        ; OFF                       ; IGNORE_CASCADE             ;
+-------------------------------+---------------------------+----------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+------------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component ;
+------------------------------------+----------------------+------------------------------------+
; Parameter Name                     ; Value                ; Type                               ;
+------------------------------------+----------------------+------------------------------------+
; BYTE_SIZE_BLOCK                    ; 8                    ; Untyped                            ;
; AUTO_CARRY_CHAINS                  ; ON                   ; AUTO_CARRY                         ;
; IGNORE_CARRY_BUFFERS               ; OFF                  ; IGNORE_CARRY                       ;
; AUTO_CASCADE_CHAINS                ; ON                   ; AUTO_CASCADE                       ;
; IGNORE_CASCADE_BUFFERS             ; OFF                  ; IGNORE_CASCADE                     ;
; WIDTH_BYTEENA                      ; 1                    ; Untyped                            ;
; OPERATION_MODE                     ; ROM                  ; Untyped                            ;
; WIDTH_A                            ; 14                   ; Signed Integer                     ;
; WIDTHAD_A                          ; 12                   ; Signed Integer                     ;
; NUMWORDS_A                         ; 4096                 ; Signed Integer                     ;
; OUTDATA_REG_A                      ; CLOCK0               ; Untyped                            ;
; ADDRESS_ACLR_A                     ; NONE                 ; Untyped                            ;
; OUTDATA_ACLR_A                     ; NONE                 ; Untyped                            ;
; WRCONTROL_ACLR_A                   ; NONE                 ; Untyped                            ;
; INDATA_ACLR_A                      ; NONE                 ; Untyped                            ;
; BYTEENA_ACLR_A                     ; NONE                 ; Untyped                            ;
; WIDTH_B                            ; 1                    ; Untyped                            ;
; WIDTHAD_B                          ; 1                    ; Untyped                            ;
; NUMWORDS_B                         ; 1                    ; Untyped                            ;
; INDATA_REG_B                       ; CLOCK1               ; Untyped                            ;
; WRCONTROL_WRADDRESS_REG_B          ; CLOCK1               ; Untyped                            ;
; RDCONTROL_REG_B                    ; CLOCK1               ; Untyped                            ;
; ADDRESS_REG_B                      ; CLOCK1               ; Untyped                            ;
; OUTDATA_REG_B                      ; UNREGISTERED         ; Untyped                            ;
; BYTEENA_REG_B                      ; CLOCK1               ; Untyped                            ;
; INDATA_ACLR_B                      ; NONE                 ; Untyped                            ;
; WRCONTROL_ACLR_B                   ; NONE                 ; Untyped                            ;
; ADDRESS_ACLR_B                     ; NONE                 ; Untyped                            ;
; OUTDATA_ACLR_B                     ; NONE                 ; Untyped                            ;
; RDCONTROL_ACLR_B                   ; NONE                 ; Untyped                            ;
; BYTEENA_ACLR_B                     ; NONE                 ; Untyped                            ;
; WIDTH_BYTEENA_A                    ; 1                    ; Signed Integer                     ;
; WIDTH_BYTEENA_B                    ; 1                    ; Untyped                            ;
; RAM_BLOCK_TYPE                     ; AUTO                 ; Untyped                            ;
; BYTE_SIZE                          ; 8                    ; Untyped                            ;
; READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE            ; Untyped                            ;
; READ_DURING_WRITE_MODE_PORT_A      ; NEW_DATA_NO_NBE_READ ; Untyped                            ;
; READ_DURING_WRITE_MODE_PORT_B      ; NEW_DATA_NO_NBE_READ ; Untyped                            ;
; INIT_FILE                          ; Sin_Wave.mif         ; Untyped                            ;
; INIT_FILE_LAYOUT                   ; PORT_A               ; Untyped                            ;
; MAXIMUM_DEPTH                      ; 0                    ; Untyped                            ;
; CLOCK_ENABLE_INPUT_A               ; BYPASS               ; Untyped                            ;
; CLOCK_ENABLE_INPUT_B               ; NORMAL               ; Untyped                            ;
; CLOCK_ENABLE_OUTPUT_A              ; BYPASS               ; Untyped                            ;
; CLOCK_ENABLE_OUTPUT_B              ; NORMAL               ; Untyped                            ;
; CLOCK_ENABLE_CORE_A                ; USE_INPUT_CLKEN      ; Untyped                            ;
; CLOCK_ENABLE_CORE_B                ; USE_INPUT_CLKEN      ; Untyped                            ;
; ENABLE_ECC                         ; FALSE                ; Untyped                            ;
; ECC_PIPELINE_STAGE_ENABLED         ; FALSE                ; Untyped                            ;
; WIDTH_ECCSTATUS                    ; 3                    ; Untyped                            ;
; DEVICE_FAMILY                      ; Cyclone IV E         ; Untyped                            ;
; CBXI_PARAMETER                     ; altsyncram_aj91      ; Untyped                            ;
+------------------------------------+----------------------+------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: ROM_Tri:ROM_Tri|altsyncram:altsyncram_component ;
+------------------------------------+----------------------+----------------------------------+
; Parameter Name                     ; Value                ; Type                             ;
+------------------------------------+----------------------+----------------------------------+
; BYTE_SIZE_BLOCK                    ; 8                    ; Untyped                          ;
; AUTO_CARRY_CHAINS                  ; ON                   ; AUTO_CARRY                       ;
; IGNORE_CARRY_BUFFERS               ; OFF                  ; IGNORE_CARRY                     ;
; AUTO_CASCADE_CHAINS                ; ON                   ; AUTO_CASCADE                     ;
; IGNORE_CASCADE_BUFFERS             ; OFF                  ; IGNORE_CASCADE                   ;
; WIDTH_BYTEENA                      ; 1                    ; Untyped                          ;
; OPERATION_MODE                     ; ROM                  ; Untyped                          ;
; WIDTH_A                            ; 14                   ; Signed Integer                   ;
; WIDTHAD_A                          ; 12                   ; Signed Integer                   ;
; NUMWORDS_A                         ; 4096                 ; Signed Integer                   ;
; OUTDATA_REG_A                      ; UNREGISTERED         ; Untyped                          ;
; ADDRESS_ACLR_A                     ; NONE                 ; Untyped                          ;
; OUTDATA_ACLR_A                     ; NONE                 ; Untyped                          ;
; WRCONTROL_ACLR_A                   ; NONE                 ; Untyped                          ;
; INDATA_ACLR_A                      ; NONE                 ; Untyped                          ;
; BYTEENA_ACLR_A                     ; NONE                 ; Untyped                          ;
; WIDTH_B                            ; 1                    ; Untyped                          ;
; WIDTHAD_B                          ; 1                    ; Untyped                          ;
; NUMWORDS_B                         ; 1                    ; Untyped                          ;
; INDATA_REG_B                       ; CLOCK1               ; Untyped                          ;
; WRCONTROL_WRADDRESS_REG_B          ; CLOCK1               ; Untyped                          ;
; RDCONTROL_REG_B                    ; CLOCK1               ; Untyped                          ;
; ADDRESS_REG_B                      ; CLOCK1               ; Untyped                          ;
; OUTDATA_REG_B                      ; UNREGISTERED         ; Untyped                          ;
; BYTEENA_REG_B                      ; CLOCK1               ; Untyped                          ;
; INDATA_ACLR_B                      ; NONE                 ; Untyped                          ;
; WRCONTROL_ACLR_B                   ; NONE                 ; Untyped                          ;
; ADDRESS_ACLR_B                     ; NONE                 ; Untyped                          ;
; OUTDATA_ACLR_B                     ; NONE                 ; Untyped                          ;
; RDCONTROL_ACLR_B                   ; NONE                 ; Untyped                          ;
; BYTEENA_ACLR_B                     ; NONE                 ; Untyped                          ;
; WIDTH_BYTEENA_A                    ; 1                    ; Signed Integer                   ;
; WIDTH_BYTEENA_B                    ; 1                    ; Untyped                          ;
; RAM_BLOCK_TYPE                     ; AUTO                 ; Untyped                          ;
; BYTE_SIZE                          ; 8                    ; Untyped                          ;
; READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE            ; Untyped                          ;
; READ_DURING_WRITE_MODE_PORT_A      ; NEW_DATA_NO_NBE_READ ; Untyped                          ;
; READ_DURING_WRITE_MODE_PORT_B      ; NEW_DATA_NO_NBE_READ ; Untyped                          ;
; INIT_FILE                          ; Tri_Wave.mif         ; Untyped                          ;
; INIT_FILE_LAYOUT                   ; PORT_A               ; Untyped                          ;
; MAXIMUM_DEPTH                      ; 0                    ; Untyped                          ;
; CLOCK_ENABLE_INPUT_A               ; BYPASS               ; Untyped                          ;
; CLOCK_ENABLE_INPUT_B               ; NORMAL               ; Untyped                          ;
; CLOCK_ENABLE_OUTPUT_A              ; BYPASS               ; Untyped                          ;
; CLOCK_ENABLE_OUTPUT_B              ; NORMAL               ; Untyped                          ;
; CLOCK_ENABLE_CORE_A                ; USE_INPUT_CLKEN      ; Untyped                          ;
; CLOCK_ENABLE_CORE_B                ; USE_INPUT_CLKEN      ; Untyped                          ;
; ENABLE_ECC                         ; FALSE                ; Untyped                          ;
; ECC_PIPELINE_STAGE_ENABLED         ; FALSE                ; Untyped                          ;
; WIDTH_ECCSTATUS                    ; 3                    ; Untyped                          ;
; DEVICE_FAMILY                      ; Cyclone IV E         ; Untyped                          ;
; CBXI_PARAMETER                     ; altsyncram_4aa1      ; Untyped                          ;
+------------------------------------+----------------------+----------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+------------------------------------------------------------------+
; Parameter Settings for Inferred Entity Instance: lpm_divide:Div0 ;
+------------------------+----------------+------------------------+
; Parameter Name         ; Value          ; Type                   ;
+------------------------+----------------+------------------------+
; LPM_WIDTHN             ; 14             ; Untyped                ;
; LPM_WIDTHD             ; 2              ; Untyped                ;
; LPM_NREPRESENTATION    ; UNSIGNED       ; Untyped                ;
; LPM_DREPRESENTATION    ; UNSIGNED       ; Untyped                ;
; LPM_PIPELINE           ; 0              ; Untyped                ;
; LPM_REMAINDERPOSITIVE  ; TRUE           ; Untyped                ;
; MAXIMIZE_SPEED         ; 5              ; Untyped                ;
; CBXI_PARAMETER         ; lpm_divide_uim ; Untyped                ;
; CARRY_CHAIN            ; MANUAL         ; Untyped                ;
; OPTIMIZE_FOR_SPEED     ; 5              ; Untyped                ;
; AUTO_CARRY_CHAINS      ; ON             ; AUTO_CARRY             ;
; IGNORE_CARRY_BUFFERS   ; OFF            ; IGNORE_CARRY           ;
; AUTO_CASCADE_CHAINS    ; ON             ; AUTO_CASCADE           ;
; IGNORE_CASCADE_BUFFERS ; OFF            ; IGNORE_CASCADE         ;
+------------------------+----------------+------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------------+
; altpll Parameter Settings by Entity Instance                              ;
+-------------------------------+-------------------------------------------+
; Name                          ; Value                                     ;
+-------------------------------+-------------------------------------------+
; Number of entity instances    ; 1                                         ;
; Entity Instance               ; PLL_CLK:u_PLL_CLK|altpll:altpll_component ;
;     -- OPERATION_MODE         ; NORMAL                                    ;
;     -- PLL_TYPE               ; AUTO                                      ;
;     -- PRIMARY_CLOCK          ; INCLK0                                    ;
;     -- INCLK0_INPUT_FREQUENCY ; 20000                                     ;
;     -- INCLK1_INPUT_FREQUENCY ; 0                                         ;
;     -- VCO_MULTIPLY_BY        ; 0                                         ;
;     -- VCO_DIVIDE_BY          ; 0                                         ;
+-------------------------------+-------------------------------------------+


+-----------------------------------------------------------------------------------------------+
; altsyncram Parameter Settings by Entity Instance                                              ;
+-------------------------------------------+---------------------------------------------------+
; Name                                      ; Value                                             ;
+-------------------------------------------+---------------------------------------------------+
; Number of entity instances                ; 2                                                 ;
; Entity Instance                           ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component ;
;     -- OPERATION_MODE                     ; ROM                                               ;
;     -- WIDTH_A                            ; 14                                                ;
;     -- NUMWORDS_A                         ; 4096                                              ;
;     -- OUTDATA_REG_A                      ; CLOCK0                                            ;
;     -- WIDTH_B                            ; 1                                                 ;
;     -- NUMWORDS_B                         ; 1                                                 ;
;     -- ADDRESS_REG_B                      ; CLOCK1                                            ;
;     -- OUTDATA_REG_B                      ; UNREGISTERED                                      ;
;     -- RAM_BLOCK_TYPE                     ; AUTO                                              ;
;     -- READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                                         ;
; Entity Instance                           ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component   ;
;     -- OPERATION_MODE                     ; ROM                                               ;
;     -- WIDTH_A                            ; 14                                                ;
;     -- NUMWORDS_A                         ; 4096                                              ;
;     -- OUTDATA_REG_A                      ; UNREGISTERED                                      ;
;     -- WIDTH_B                            ; 1                                                 ;
;     -- NUMWORDS_B                         ; 1                                                 ;
;     -- ADDRESS_REG_B                      ; CLOCK1                                            ;
;     -- OUTDATA_REG_B                      ; UNREGISTERED                                      ;
;     -- RAM_BLOCK_TYPE                     ; AUTO                                              ;
;     -- READ_DURING_WRITE_MODE_MIXED_PORTS ; DONT_CARE                                         ;
+-------------------------------------------+---------------------------------------------------+


+-------------------------------+
; Elapsed Time Per Partition    ;
+----------------+--------------+
; Partition Name ; Elapsed Time ;
+----------------+--------------+
; Top            ; 00:00:00     ;
+----------------+--------------+


+-------------------------------+
; Analysis & Synthesis Messages ;
+-------------------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit Analysis & Synthesis
    Info: Version 13.1.0 Build 162 10/23/2013 SJ Web Edition
    Info: Processing started: Thu Jul 31 17:43:21 2025
Info: Command: quartus_map --read_settings_files=on --write_settings_files=off DAC904 -c DAC904
Warning (20028): Parallel compilation is not licensed and has been disabled
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1/fpga_cyclone_iv_e22/ip_core/rom/rom_tri.v
    Info (12023): Found entity 1: ROM_Tri
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1/fpga_cyclone_iv_e22/rtl/sel_wave.v
    Info (12023): Found entity 1: sel_wave
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1/fpga_cyclone_iv_e22/rtl/key_delay.v
    Info (12023): Found entity 1: key_delay
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1/fpga_cyclone_iv_e22/rtl/key_con.v
    Info (12023): Found entity 1: key_con
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1/fpga_cyclone_iv_e22/rtl/add_32bit.v
    Info (12023): Found entity 1: add_32bit
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1/fpga_cyclone_iv_e22/ip_core/rom/rom_sin.v
    Info (12023): Found entity 1: ROM_Sin
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1/fpga_cyclone_iv_e22/rtl/dac904_top.v
    Info (12023): Found entity 1: DAC904_TOP
Info (12021): Found 1 design units, including 1 entities, in source file /users/kindred.c/downloads/zuolan_v4.1/fpga_cyclone_iv_e22/ip_core/pll/pll_clk.v
    Info (12023): Found entity 1: PLL_CLK
Info (12127): Elaborating entity "DAC904_TOP" for the top level hierarchy
Info (12128): Elaborating entity "PLL_CLK" for hierarchy "PLL_CLK:u_PLL_CLK"
Info (12128): Elaborating entity "altpll" for hierarchy "PLL_CLK:u_PLL_CLK|altpll:altpll_component"
Info (12130): Elaborated megafunction instantiation "PLL_CLK:u_PLL_CLK|altpll:altpll_component"
Info (12133): Instantiated megafunction "PLL_CLK:u_PLL_CLK|altpll:altpll_component" with the following parameter:
    Info (12134): Parameter "bandwidth_type" = "AUTO"
    Info (12134): Parameter "clk0_divide_by" = "5"
    Info (12134): Parameter "clk0_duty_cycle" = "50"
    Info (12134): Parameter "clk0_multiply_by" = "2"
    Info (12134): Parameter "clk0_phase_shift" = "0"
    Info (12134): Parameter "compensate_clock" = "CLK0"
    Info (12134): Parameter "inclk0_input_frequency" = "20000"
    Info (12134): Parameter "intended_device_family" = "Cyclone IV E"
    Info (12134): Parameter "lpm_hint" = "CBX_MODULE_PREFIX=PLL_CLK"
    Info (12134): Parameter "lpm_type" = "altpll"
    Info (12134): Parameter "operation_mode" = "NORMAL"
    Info (12134): Parameter "pll_type" = "AUTO"
    Info (12134): Parameter "port_activeclock" = "PORT_UNUSED"
    Info (12134): Parameter "port_areset" = "PORT_USED"
    Info (12134): Parameter "port_clkbad0" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkbad1" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkloss" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkswitch" = "PORT_UNUSED"
    Info (12134): Parameter "port_configupdate" = "PORT_UNUSED"
    Info (12134): Parameter "port_fbin" = "PORT_UNUSED"
    Info (12134): Parameter "port_inclk0" = "PORT_USED"
    Info (12134): Parameter "port_inclk1" = "PORT_UNUSED"
    Info (12134): Parameter "port_locked" = "PORT_UNUSED"
    Info (12134): Parameter "port_pfdena" = "PORT_UNUSED"
    Info (12134): Parameter "port_phasecounterselect" = "PORT_UNUSED"
    Info (12134): Parameter "port_phasedone" = "PORT_UNUSED"
    Info (12134): Parameter "port_phasestep" = "PORT_UNUSED"
    Info (12134): Parameter "port_phaseupdown" = "PORT_UNUSED"
    Info (12134): Parameter "port_pllena" = "PORT_UNUSED"
    Info (12134): Parameter "port_scanaclr" = "PORT_UNUSED"
    Info (12134): Parameter "port_scanclk" = "PORT_UNUSED"
    Info (12134): Parameter "port_scanclkena" = "PORT_UNUSED"
    Info (12134): Parameter "port_scandata" = "PORT_UNUSED"
    Info (12134): Parameter "port_scandataout" = "PORT_UNUSED"
    Info (12134): Parameter "port_scandone" = "PORT_UNUSED"
    Info (12134): Parameter "port_scanread" = "PORT_UNUSED"
    Info (12134): Parameter "port_scanwrite" = "PORT_UNUSED"
    Info (12134): Parameter "port_clk0" = "PORT_USED"
    Info (12134): Parameter "port_clk1" = "PORT_UNUSED"
    Info (12134): Parameter "port_clk2" = "PORT_UNUSED"
    Info (12134): Parameter "port_clk3" = "PORT_UNUSED"
    Info (12134): Parameter "port_clk4" = "PORT_UNUSED"
    Info (12134): Parameter "port_clk5" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena0" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena1" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena2" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena3" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena4" = "PORT_UNUSED"
    Info (12134): Parameter "port_clkena5" = "PORT_UNUSED"
    Info (12134): Parameter "port_extclk0" = "PORT_UNUSED"
    Info (12134): Parameter "port_extclk1" = "PORT_UNUSED"
    Info (12134): Parameter "port_extclk2" = "PORT_UNUSED"
    Info (12134): Parameter "port_extclk3" = "PORT_UNUSED"
    Info (12134): Parameter "width_clock" = "5"
Info (12021): Found 1 design units, including 1 entities, in source file db/pll_clk_altpll.v
    Info (12023): Found entity 1: PLL_CLK_altpll
Info (12128): Elaborating entity "PLL_CLK_altpll" for hierarchy "PLL_CLK:u_PLL_CLK|altpll:altpll_component|PLL_CLK_altpll:auto_generated"
Info (12128): Elaborating entity "ROM_Sin" for hierarchy "ROM_Sin:u_ROM_Sin"
Info (12128): Elaborating entity "altsyncram" for hierarchy "ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component"
Info (12130): Elaborated megafunction instantiation "ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component"
Info (12133): Instantiated megafunction "ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component" with the following parameter:
    Info (12134): Parameter "address_aclr_a" = "NONE"
    Info (12134): Parameter "clock_enable_input_a" = "BYPASS"
    Info (12134): Parameter "clock_enable_output_a" = "BYPASS"
    Info (12134): Parameter "init_file" = "Sin_Wave.mif"
    Info (12134): Parameter "intended_device_family" = "Cyclone IV E"
    Info (12134): Parameter "lpm_hint" = "ENABLE_RUNTIME_MOD=NO"
    Info (12134): Parameter "lpm_type" = "altsyncram"
    Info (12134): Parameter "numwords_a" = "4096"
    Info (12134): Parameter "operation_mode" = "ROM"
    Info (12134): Parameter "outdata_aclr_a" = "NONE"
    Info (12134): Parameter "outdata_reg_a" = "CLOCK0"
    Info (12134): Parameter "widthad_a" = "12"
    Info (12134): Parameter "width_a" = "14"
    Info (12134): Parameter "width_byteena_a" = "1"
Info (12021): Found 1 design units, including 1 entities, in source file db/altsyncram_aj91.tdf
    Info (12023): Found entity 1: altsyncram_aj91
Info (12128): Elaborating entity "altsyncram_aj91" for hierarchy "ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated"
Info (12128): Elaborating entity "ROM_Tri" for hierarchy "ROM_Tri:ROM_Tri"
Info (12128): Elaborating entity "altsyncram" for hierarchy "ROM_Tri:ROM_Tri|altsyncram:altsyncram_component"
Info (12130): Elaborated megafunction instantiation "ROM_Tri:ROM_Tri|altsyncram:altsyncram_component"
Info (12133): Instantiated megafunction "ROM_Tri:ROM_Tri|altsyncram:altsyncram_component" with the following parameter:
    Info (12134): Parameter "address_aclr_a" = "NONE"
    Info (12134): Parameter "clock_enable_input_a" = "BYPASS"
    Info (12134): Parameter "clock_enable_output_a" = "BYPASS"
    Info (12134): Parameter "init_file" = "Tri_Wave.mif"
    Info (12134): Parameter "intended_device_family" = "Cyclone IV E"
    Info (12134): Parameter "lpm_hint" = "ENABLE_RUNTIME_MOD=NO"
    Info (12134): Parameter "lpm_type" = "altsyncram"
    Info (12134): Parameter "numwords_a" = "4096"
    Info (12134): Parameter "operation_mode" = "ROM"
    Info (12134): Parameter "outdata_aclr_a" = "NONE"
    Info (12134): Parameter "outdata_reg_a" = "UNREGISTERED"
    Info (12134): Parameter "widthad_a" = "12"
    Info (12134): Parameter "width_a" = "14"
    Info (12134): Parameter "width_byteena_a" = "1"
Info (12021): Found 1 design units, including 1 entities, in source file db/altsyncram_4aa1.tdf
    Info (12023): Found entity 1: altsyncram_4aa1
Info (12128): Elaborating entity "altsyncram_4aa1" for hierarchy "ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated"
Info (12128): Elaborating entity "add_32bit" for hierarchy "add_32bit:u_add_32bit"
Info (12128): Elaborating entity "key_con" for hierarchy "key_con:u_key_con"
Info (12128): Elaborating entity "key_delay" for hierarchy "key_con:u_key_con|key_delay:u_key1_delay"
Info (12128): Elaborating entity "sel_wave" for hierarchy "sel_wave:u_sel_wave"
Info (278001): Inferred 1 megafunctions from design logic
    Info (278004): Inferred divider/modulo megafunction ("lpm_divide") from the following logic: "Div0"
Info (12130): Elaborated megafunction instantiation "lpm_divide:Div0"
Info (12133): Instantiated megafunction "lpm_divide:Div0" with the following parameter:
    Info (12134): Parameter "LPM_WIDTHN" = "14"
    Info (12134): Parameter "LPM_WIDTHD" = "2"
    Info (12134): Parameter "LPM_NREPRESENTATION" = "UNSIGNED"
    Info (12134): Parameter "LPM_DREPRESENTATION" = "UNSIGNED"
Info (12021): Found 1 design units, including 1 entities, in source file db/lpm_divide_uim.tdf
    Info (12023): Found entity 1: lpm_divide_uim
Info (12021): Found 1 design units, including 1 entities, in source file db/sign_div_unsign_mlh.tdf
    Info (12023): Found entity 1: sign_div_unsign_mlh
Info (12021): Found 1 design units, including 1 entities, in source file db/alt_u_div_07f.tdf
    Info (12023): Found entity 1: alt_u_div_07f
Info (12021): Found 1 design units, including 1 entities, in source file db/add_sub_7pc.tdf
    Info (12023): Found entity 1: add_sub_7pc
Info (12021): Found 1 design units, including 1 entities, in source file db/add_sub_8pc.tdf
    Info (12023): Found entity 1: add_sub_8pc
Info (13000): Registers with preset signals will power-up high
Info (13003): DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back
Warning (13024): Output pins are stuck at VCC or GND
    Warning (13410): Pin "PD" is stuck at GND
Info (286030): Timing-Driven Synthesis is running
Info (16010): Generating hard_block partition "hard_block:auto_generated_inst"
    Info (16011): Adding 1 node(s), including 0 DDIO, 1 PLL, 0 transceiver and 0 LCELL
Info (21057): Implemented 934 device resources after synthesis - the final resource count might be different
    Info (21058): Implemented 5 input pins
    Info (21059): Implemented 16 output pins
    Info (21061): Implemented 884 logic cells
    Info (21064): Implemented 28 RAM segments
    Info (21065): Implemented 1 PLLs
Info: Quartus II 64-Bit Analysis & Synthesis was successful. 0 errors, 3 warnings
    Info: Peak virtual memory: 4661 megabytes
    Info: Processing ended: Thu Jul 31 17:43:23 2025
    Info: Elapsed time: 00:00:02
    Info: Total CPU time (on all processors): 00:00:01


