TimeQuest Timing Analyzer report for DAC904
Thu Jul 31 21:11:31 2025
Quartus II 64-Bit Version 13.1.0 Build 162 10/23/2013 SJ Web Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. TimeQuest Timing Analyzer Summary
  3. Parallel Compilation
  4. SDC File List
  5. Clocks
  6. Slow 1200mV 85C Model Fmax Summary
  7. Timing Closure Recommendations
  8. Slow 1200mV 85C Model Setup Summary
  9. Slow 1200mV 85C Model Hold Summary
 10. Slow 1200mV 85C Model Recovery Summary
 11. Slow 1200mV 85C Model Removal Summary
 12. Slow 1200mV 85C Model Minimum Pulse Width Summary
 13. Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 14. Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 15. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'
 16. Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'
 17. Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 18. Setup Times
 19. Hold Times
 20. Clock to Output Times
 21. Minimum Clock to Output Times
 22. Slow 1200mV 85C Model Metastability Report
 23. Slow 1200mV 0C Model Fmax Summary
 24. Slow 1200mV 0C Model Setup Summary
 25. Slow 1200mV 0C Model Hold Summary
 26. Slow 1200mV 0C Model Recovery Summary
 27. Slow 1200mV 0C Model Removal Summary
 28. Slow 1200mV 0C Model Minimum Pulse Width Summary
 29. Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 30. Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 31. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 32. Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 33. Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 34. Setup Times
 35. Hold Times
 36. Clock to Output Times
 37. Minimum Clock to Output Times
 38. Slow 1200mV 0C Model Metastability Report
 39. Fast 1200mV 0C Model Setup Summary
 40. Fast 1200mV 0C Model Hold Summary
 41. Fast 1200mV 0C Model Recovery Summary
 42. Fast 1200mV 0C Model Removal Summary
 43. Fast 1200mV 0C Model Minimum Pulse Width Summary
 44. Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 45. Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 46. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'
 47. Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'
 48. Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'
 49. Setup Times
 50. Hold Times
 51. Clock to Output Times
 52. Minimum Clock to Output Times
 53. Fast 1200mV 0C Model Metastability Report
 54. Multicorner Timing Analysis Summary
 55. Setup Times
 56. Hold Times
 57. Clock to Output Times
 58. Minimum Clock to Output Times
 59. Board Trace Model Assignments
 60. Input Transition Times
 61. Signal Integrity Metrics (Slow 1200mv 0c Model)
 62. Signal Integrity Metrics (Slow 1200mv 85c Model)
 63. Signal Integrity Metrics (Fast 1200mv 0c Model)
 64. Setup Transfers
 65. Hold Transfers
 66. Report TCCS
 67. Report RSKM
 68. Unconstrained Paths
 69. TimeQuest Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 1991-2013 Altera Corporation
Your use of Altera Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Altera Program License 
Subscription Agreement, Altera MegaCore Function License 
Agreement, or other applicable license agreement, including, 
without limitation, that your use is for the sole purpose of 
programming logic devices manufactured by Altera and sold by 
Altera or its authorized distributors.  Please refer to the 
applicable agreement for further details.



+-------------------------------------------------------------------------+
; TimeQuest Timing Analyzer Summary                                       ;
+--------------------+----------------------------------------------------+
; Quartus II Version ; Version 13.1.0 Build 162 10/23/2013 SJ Web Edition ;
; Revision Name      ; DAC904                                             ;
; Device Family      ; Cyclone IV E                                       ;
; Device Name        ; EP4CE6E22C8                                        ;
; Timing Models      ; Final                                              ;
; Delay Model        ; Combined                                           ;
; Rise/Fall Delays   ; Enabled                                            ;
+--------------------+----------------------------------------------------+


Parallel compilation was disabled, but you have multiple processors available. Enable parallel compilation to reduce compilation time.
+-------------------------------------+
; Parallel Compilation                ;
+----------------------------+--------+
; Processors                 ; Number ;
+----------------------------+--------+
; Number detected on machine ; 20     ;
; Maximum allowed            ; 1      ;
+----------------------------+--------+


+-----------------------------------------------------+
; SDC File List                                       ;
+-----------------+--------+--------------------------+
; SDC File Path   ; Status ; Read at                  ;
+-----------------+--------+--------------------------+
; ../doc/SDC1.sdc ; OK     ; Thu Jul 31 21:11:30 2025 ;
+-----------------+--------+--------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                                                                                                                                                                                                                                                                                                             ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clock Name                                            ; Type      ; Period ; Frequency  ; Rise  ; Fall   ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master  ; Source                                                  ; Targets                                                                                                                                                                                 ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; CLK_50M                                               ; Base      ; 20.000 ; 50.0 MHz   ; 0.000 ; 10.000 ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { SYS_CLK }                                                                                                                                                                             ;
; CLK_165M                                              ; Base      ; 6.060  ; 165.02 MHz ; 0.000 ; 3.030  ;            ;           ;             ;       ;        ;           ;            ;          ;         ;                                                         ; { DAC_CLK DAC_DATA[0] DAC_DATA[1] DAC_DATA[2] DAC_DATA[3] DAC_DATA[4] DAC_DATA[5] DAC_DATA[6] DAC_DATA[7] DAC_DATA[8] DAC_DATA[9] DAC_DATA[10] DAC_DATA[11] DAC_DATA[12] DAC_DATA[13] } ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Generated ; 50.000 ; 20.0 MHz   ; 0.000 ; 25.000 ; 50.00      ; 5         ; 2           ;       ;        ;           ;            ; false    ; CLK_50M ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0] ; { u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] }                                                                                                                               ;
+-------------------------------------------------------+-----------+--------+------------+-------+--------+------------+-----------+-------------+-------+--------+-----------+------------+----------+---------+---------------------------------------------------------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+


+---------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary                                                          ;
+------------+-----------------+-------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                            ; Note ;
+------------+-----------------+-------------------------------------------------------+------+
; 129.92 MHz ; 129.92 MHz      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+------------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


----------------------------------
; Timing Closure Recommendations ;
----------------------------------
HTML report is unavailable in plain text report export.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup Summary                                            ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 42.303 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold Summary                                            ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.408 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary                              ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.934  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.719 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                                                                                       ; To Node                                         ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 42.303 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.493     ; 7.205      ;
; 42.571 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[0]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.494     ; 6.936      ;
; 42.871 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.494     ; 6.636      ;
; 42.981 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.492     ; 6.528      ;
; 43.109 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[6]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.493     ; 6.399      ;
; 43.688 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[5]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.492     ; 5.821      ;
; 43.828 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.476     ; 5.697      ;
; 44.130 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.473     ; 5.398      ;
; 44.164 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[12]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.475     ; 5.362      ;
; 44.203 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.473     ; 5.325      ;
; 44.460 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[13]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.475     ; 5.066      ;
; 44.497 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.473     ; 5.031      ;
; 44.546 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.476     ; 4.979      ;
; 44.925 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.473     ; 4.603      ;
; 45.086 ; key_con:u_key_con|key_delay:u_key3_delay|kh[22]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.866      ;
; 45.134 ; key_con:u_key_con|key_delay:u_key2_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.785      ;
; 45.208 ; key_con:u_key_con|key_delay:u_key2_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.711      ;
; 45.331 ; key_con:u_key_con|key_delay:u_key2_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.588      ;
; 45.459 ; key_con:u_key_con|key_delay:u_key3_delay|kh[21]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.493      ;
; 45.568 ; key_con:u_key_con|key_delay:u_key2_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.351      ;
; 45.599 ; key_con:u_key_con|key_delay:u_key3_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.353      ;
; 45.600 ; key_con:u_key_con|key_delay:u_key3_delay|kh[20]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.352      ;
; 45.611 ; key_con:u_key_con|key_delay:u_key3_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.341      ;
; 45.620 ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.332      ;
; 45.752 ; key_con:u_key_con|key_delay:u_key3_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.200      ;
; 45.754 ; key_con:u_key_con|key_delay:u_key3_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.198      ;
; 45.792 ; key_con:u_key_con|key_delay:u_key3_delay|kh[19]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.160      ;
; 45.811 ; key_con:u_key_con|key_delay:u_key3_delay|kh[10]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.051     ; 4.139      ;
; 45.821 ; key_con:u_key_con|key_delay:u_key3_delay|kh[29]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.131      ;
; 45.846 ; key_con:u_key_con|key_delay:u_key3_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.106      ;
; 45.881 ; key_con:u_key_con|key_delay:u_key3_delay|kl[15]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.071      ;
; 45.904 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.015      ;
; 45.904 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.015      ;
; 45.904 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.015      ;
; 45.904 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 4.015      ;
; 45.905 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[31]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.015      ;
; 45.905 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 4.015      ;
; 45.911 ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.041      ;
; 45.919 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.067     ; 4.015      ;
; 45.923 ; key_con:u_key_con|key_delay:u_key3_delay|kh[7]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.051     ; 4.027      ;
; 45.932 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[2]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.090     ; 3.979      ;
; 45.934 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[13]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.090     ; 3.977      ;
; 45.934 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[9]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.090     ; 3.977      ;
; 45.936 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.983      ;
; 45.936 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.983      ;
; 45.936 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.983      ;
; 45.936 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.983      ;
; 45.937 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[30]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.983      ;
; 45.937 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.983      ;
; 45.944 ; key_con:u_key_con|key_delay:u_key3_delay|kh[27]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 4.008      ;
; 45.951 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.067     ; 3.983      ;
; 45.958 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.083     ; 3.960      ;
; 45.966 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.953      ;
; 45.966 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.953      ;
; 45.966 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.953      ;
; 45.966 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.953      ;
; 45.967 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[31]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.953      ;
; 45.967 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.953      ;
; 45.972 ; key_con:u_key_con|key_delay:u_key1_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.083     ; 3.946      ;
; 45.975 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[11]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.090     ; 3.936      ;
; 45.975 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[1]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.090     ; 3.936      ;
; 45.976 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[12]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.090     ; 3.935      ;
; 45.976 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[8]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.090     ; 3.935      ;
; 45.977 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[10]              ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.090     ; 3.934      ;
; 45.977 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[4]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.090     ; 3.934      ;
; 45.979 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[7]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.090     ; 3.932      ;
; 45.979 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[3]               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.090     ; 3.932      ;
; 45.981 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.067     ; 3.953      ;
; 46.035 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.884      ;
; 46.035 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.884      ;
; 46.035 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.884      ;
; 46.035 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.884      ;
; 46.036 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[30]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.884      ;
; 46.036 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.884      ;
; 46.041 ; key_con:u_key_con|key_delay:u_key3_delay|kh[8]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.051     ; 3.909      ;
; 46.050 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.869      ;
; 46.050 ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.869      ;
; 46.050 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.067     ; 3.884      ;
; 46.050 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.869      ;
; 46.050 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.869      ;
; 46.050 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.869      ;
; 46.050 ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.869      ;
; 46.050 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.869      ;
; 46.050 ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.869      ;
; 46.051 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[29]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.869      ;
; 46.051 ; add_32bit:u_add_32bit|add[2]                                                                                    ; add_32bit:u_add_32bit|add[31]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.869      ;
; 46.051 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.869      ;
; 46.051 ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.869      ;
; 46.065 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[29] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.067     ; 3.869      ;
; 46.065 ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.067     ; 3.869      ;
; 46.082 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.837      ;
; 46.082 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.837      ;
; 46.082 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.837      ;
; 46.082 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.837      ;
; 46.083 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[28]                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.837      ;
; 46.083 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[28] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.081     ; 3.837      ;
; 46.083 ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.083     ; 3.835      ;
; 46.084 ; key_con:u_key_con|key_delay:u_key3_delay|kh[16]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.049     ; 3.868      ;
; 46.085 ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.834      ;
; 46.085 ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[30] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.082     ; 3.834      ;
+--------+-----------------------------------------------------------------------------------------------------------------+-------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                 ;
+-------+-------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                       ; To Node                                                                                                          ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.408 ; add_32bit:u_add_32bit|add[21]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.144      ;
; 0.479 ; add_32bit:u_add_32bit|add[29]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.215      ;
; 0.692 ; add_32bit:u_add_32bit|add[28]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.428      ;
; 0.707 ; add_32bit:u_add_32bit|add[21]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.443      ;
; 0.730 ; add_32bit:u_add_32bit|add[24]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.482      ; 1.466      ;
; 0.734 ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.028      ;
; 0.734 ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.028      ;
; 0.734 ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.028      ;
; 0.735 ; add_32bit:u_add_32bit|add[25]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.494      ; 1.483      ;
; 0.735 ; add_32bit:u_add_32bit|add[3]                    ; add_32bit:u_add_32bit|add[3]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.028      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.735 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.028      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.736 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.029      ;
; 0.737 ; add_32bit:u_add_32bit|add[18]                   ; add_32bit:u_add_32bit|add[18]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[17]                   ; add_32bit:u_add_32bit|add[17]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[15]                   ; add_32bit:u_add_32bit|add[15]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[12]                   ; add_32bit:u_add_32bit|add[12]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; add_32bit:u_add_32bit|add[9]                    ; add_32bit:u_add_32bit|add[9]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.030      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.029      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.029      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.737 ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[19]                   ; add_32bit:u_add_32bit|add[19]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[16]                   ; add_32bit:u_add_32bit|add[16]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[13]                   ; add_32bit:u_add_32bit|add[13]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[11]                   ; add_32bit:u_add_32bit|add[11]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[8]                    ; add_32bit:u_add_32bit|add[8]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[7]                    ; add_32bit:u_add_32bit|add[7]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[6]                    ; add_32bit:u_add_32bit|add[6]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[5]                    ; add_32bit:u_add_32bit|add[5]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[2]                    ; add_32bit:u_add_32bit|add[2]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; add_32bit:u_add_32bit|add[1]                    ; add_32bit:u_add_32bit|add[1]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.031      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.032      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.032      ;
; 0.738 ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.032      ;
; 0.739 ; add_32bit:u_add_32bit|add[14]                   ; add_32bit:u_add_32bit|add[14]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; add_32bit:u_add_32bit|add[10]                   ; add_32bit:u_add_32bit|add[10]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; add_32bit:u_add_32bit|add[4]                    ; add_32bit:u_add_32bit|add[4]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.739 ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.032      ;
; 0.740 ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.032      ;
; 0.742 ; add_32bit:u_add_32bit|add[29]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.494      ; 1.490      ;
; 0.756 ; add_32bit:u_add_32bit|add[0]                    ; add_32bit:u_add_32bit|add[0]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.049      ;
; 0.759 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15] ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.053      ;
; 0.759 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15] ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.053      ;
; 0.759 ; key_con:u_key_con|key_delay:u_key2_delay|kh[15] ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.053      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key3_delay|kh[15] ; key_con:u_key_con|key_delay:u_key3_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.053      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kh[19] ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15] ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.053      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kl[19] ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13] ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kl[11] ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kl[19] ; key_con:u_key_con|key_delay:u_key2_delay|kl[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kl[13] ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kl[11] ; key_con:u_key_con|key_delay:u_key2_delay|kl[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kh[19] ; key_con:u_key_con|key_delay:u_key2_delay|kh[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kh[13] ; key_con:u_key_con|key_delay:u_key2_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kh[11] ; key_con:u_key_con|key_delay:u_key2_delay|kh[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key3_delay|kl[19] ; key_con:u_key_con|key_delay:u_key3_delay|kl[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key3_delay|kl[15] ; key_con:u_key_con|key_delay:u_key3_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.080      ; 1.053      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key3_delay|kh[13] ; key_con:u_key_con|key_delay:u_key3_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key3_delay|kh[11] ; key_con:u_key_con|key_delay:u_key3_delay|kh[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[29] ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[27] ; key_con:u_key_con|key_delay:u_key1_delay|kh[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[21] ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[17] ; key_con:u_key_con|key_delay:u_key1_delay|kh[17]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13] ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[11] ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[29] ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[27] ; key_con:u_key_con|key_delay:u_key1_delay|kl[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[21] ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key1_delay|kl[17] ; key_con:u_key_con|key_delay:u_key1_delay|kl[17]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key2_delay|kl[29] ; key_con:u_key_con|key_delay:u_key2_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key2_delay|kl[27] ; key_con:u_key_con|key_delay:u_key2_delay|kl[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; key_con:u_key_con|key_delay:u_key2_delay|kl[21] ; key_con:u_key_con|key_delay:u_key2_delay|kl[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.082      ; 1.055      ;
+-------+-------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_165M'                                    ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'CLK_50M'                                                                                               ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.934  ; 9.934        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.954  ; 9.954        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.977  ; 9.977        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.022 ; 10.022       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.046 ; 10.046       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.066 ; 10.066       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                               ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                          ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------+
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[16] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[17] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[18] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[19] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[20] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[21] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[22] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[23] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[24] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[25] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[26] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[27] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[28] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[29] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[30] ;
; 24.719 ; 24.939       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[10] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[11] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[12] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[13] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[14] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[15] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[7]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[8]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[9]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[10] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[11] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[12] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[13] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[14] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[15] ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[8]  ;
; 24.720 ; 24.940       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[9]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[10] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[11] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[12] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[13] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[14] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[15] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[16] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[17] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[18] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[19] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[20] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[21] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[22] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[23] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[24] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[25] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[26] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[27] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[28] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[29] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[30] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[31] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[7]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[8]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[9]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[10] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[11] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[12] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[13] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[14] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[15] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[16] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[17] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[18] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[19] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]  ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[20] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[21] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[22] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[23] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[24] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[25] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[26] ;
; 24.722 ; 24.942       ; 0.220          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[27] ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 3.802 ; 4.103 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 2.847 ; 2.941 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 3.802 ; 4.103 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 3.391 ; 3.549 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 2.881 ; 3.187 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -1.972 ; -2.191 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -1.972 ; -2.191 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -2.612 ; -2.913 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -2.231 ; -2.498 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -1.917 ; -2.157 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                           ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 3.199 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 9.726 ; 9.653 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 7.289 ; 7.157 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 8.152 ; 7.966 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 8.431 ; 8.211 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 8.494 ; 8.291 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 8.564 ; 8.342 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 9.726 ; 9.653 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 8.441 ; 8.250 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 8.522 ; 8.347 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 8.639 ; 8.478 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 8.731 ; 8.530 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 8.804 ; 8.618 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 9.227 ; 9.021 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 9.346 ; 9.159 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 9.310 ; 9.023 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 3.157 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 2.703 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 5.962 ; 5.784 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 6.415 ; 6.322 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 6.587 ; 6.335 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 6.239 ; 6.034 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 6.266 ; 6.070 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 6.234 ; 6.027 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 7.429 ; 7.363 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 5.962 ; 5.784 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 6.023 ; 5.855 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 5.995 ; 5.847 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 6.046 ; 5.854 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 6.035 ; 5.864 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 6.374 ; 6.177 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 6.181 ; 6.008 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 6.528 ; 6.233 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 2.662 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


----------------------------------------------
; Slow 1200mV 85C Model Metastability Report ;
----------------------------------------------
No synchronizer chains to report.


+---------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary                                                           ;
+------------+-----------------+-------------------------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                                            ; Note ;
+------------+-----------------+-------------------------------------------------------+------+
; 139.84 MHz ; 139.84 MHz      ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;      ;
+------------+-----------------+-------------------------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 42.849 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.386 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 1.616  ; 0.000         ;
; CLK_50M                                               ; 9.943  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.718 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                                                                                  ;
+--------+-----------------------------------------------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                                                                                       ; To Node                                                                                                          ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+-----------------------------------------------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 42.849 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.433     ; 6.720      ;
; 43.112 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.434     ; 6.456      ;
; 43.378 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.434     ; 6.190      ;
; 43.492 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.432     ; 6.078      ;
; 43.605 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.433     ; 5.964      ;
; 44.177 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.432     ; 5.393      ;
; 44.292 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.417     ; 5.293      ;
; 44.586 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[10]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.418     ; 4.998      ;
; 44.627 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[12]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.417     ; 4.958      ;
; 44.668 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[11]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.418     ; 4.916      ;
; 44.898 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[13]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.417     ; 4.687      ;
; 44.942 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.418     ; 4.642      ;
; 44.985 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.417     ; 4.600      ;
; 45.320 ; key_con:u_key_con|key_delay:u_key3_delay|kh[22]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 4.641      ;
; 45.348 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.418     ; 4.236      ;
; 45.456 ; key_con:u_key_con|key_delay:u_key2_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 4.473      ;
; 45.526 ; key_con:u_key_con|key_delay:u_key2_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 4.403      ;
; 45.640 ; key_con:u_key_con|key_delay:u_key2_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 4.289      ;
; 45.689 ; key_con:u_key_con|key_delay:u_key3_delay|kh[21]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 4.272      ;
; 45.816 ; key_con:u_key_con|key_delay:u_key3_delay|kh[20]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 4.145      ;
; 45.844 ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 4.117      ;
; 45.851 ; key_con:u_key_con|key_delay:u_key3_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 4.108      ;
; 45.870 ; key_con:u_key_con|key_delay:u_key2_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 4.059      ;
; 45.926 ; key_con:u_key_con|key_delay:u_key3_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 4.033      ;
; 45.990 ; key_con:u_key_con|key_delay:u_key3_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.969      ;
; 45.995 ; key_con:u_key_con|key_delay:u_key3_delay|kh[10]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.044     ; 3.963      ;
; 45.999 ; key_con:u_key_con|key_delay:u_key3_delay|kh[19]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 3.962      ;
; 46.001 ; key_con:u_key_con|key_delay:u_key3_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.958      ;
; 46.030 ; key_con:u_key_con|key_delay:u_key3_delay|kh[29]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 3.931      ;
; 46.105 ; key_con:u_key_con|key_delay:u_key3_delay|kh[7]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.044     ; 3.853      ;
; 46.112 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.812      ;
; 46.114 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[13]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.810      ;
; 46.114 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.810      ;
; 46.123 ; key_con:u_key_con|key_delay:u_key3_delay|kl[15]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.836      ;
; 46.124 ; key_con:u_key_con|key_delay:u_key3_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.835      ;
; 46.140 ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.043     ; 3.819      ;
; 46.145 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.779      ;
; 46.146 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[12]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.778      ;
; 46.146 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[11]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.778      ;
; 46.147 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.777      ;
; 46.147 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.777      ;
; 46.148 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[10]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.776      ;
; 46.150 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.774      ;
; 46.150 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.774      ;
; 46.159 ; key_con:u_key_con|key_delay:u_key3_delay|kh[27]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 3.802      ;
; 46.199 ; key_con:u_key_con|key_delay:u_key1_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.729      ;
; 46.216 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.712      ;
; 46.220 ; key_con:u_key_con|key_delay:u_key3_delay|kh[8]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.044     ; 3.738      ;
; 46.265 ; key_con:u_key_con|key_delay:u_key3_delay|kh[16]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 3.696      ;
; 46.309 ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.619      ;
; 46.311 ; add_32bit:u_add_32bit|add[23]                                                                                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; 0.284      ; 4.012      ;
; 46.317 ; add_32bit:u_add_32bit|add[23]                                                                                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; 0.284      ; 4.006      ;
; 46.327 ; key_con:u_key_con|key_delay:u_key2_delay|kh[14]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.602      ;
; 46.362 ; key_con:u_key_con|key_delay:u_key3_delay|kh[9]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.044     ; 3.596      ;
; 46.370 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.075     ; 3.557      ;
; 46.371 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.557      ;
; 46.371 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.557      ;
; 46.372 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.557      ;
; 46.372 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.557      ;
; 46.373 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[31]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.557      ;
; 46.385 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.060     ; 3.557      ;
; 46.387 ; key_con:u_key_con|key_delay:u_key1_delay|kh[28]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.543      ;
; 46.390 ; key_con:u_key_con|key_delay:u_key1_delay|kh[23]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.540      ;
; 46.405 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.519      ;
; 46.408 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.516      ;
; 46.408 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.078     ; 3.516      ;
; 46.418 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.075     ; 3.509      ;
; 46.418 ; key_con:u_key_con|key_delay:u_key3_delay|kl[16]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.056     ; 3.528      ;
; 46.419 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.509      ;
; 46.419 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.509      ;
; 46.420 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.509      ;
; 46.420 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.509      ;
; 46.421 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[30]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.509      ;
; 46.425 ; key_con:u_key_con|key_delay:u_key1_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.503      ;
; 46.433 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.060     ; 3.509      ;
; 46.454 ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                         ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.400     ; 3.148      ;
; 46.455 ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                        ; sel_wave:u_sel_wave|da_out_reg[13]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.394     ; 3.153      ;
; 46.457 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.075     ; 3.470      ;
; 46.458 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.470      ;
; 46.458 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.470      ;
; 46.459 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.470      ;
; 46.459 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.470      ;
; 46.460 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[31]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.470      ;
; 46.464 ; key_con:u_key_con|key_delay:u_key3_delay|kh[25]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 3.497      ;
; 46.472 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.060     ; 3.470      ;
; 46.492 ; key_con:u_key_con|key_delay:u_key3_delay|kh[28]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.041     ; 3.469      ;
; 46.492 ; key_con:u_key_con|key_delay:u_key2_delay|kl[26]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.437      ;
; 46.496 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.075     ; 3.431      ;
; 46.496 ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.075     ; 3.431      ;
; 46.497 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.431      ;
; 46.497 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.431      ;
; 46.497 ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.431      ;
; 46.497 ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.074     ; 3.431      ;
; 46.498 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.431      ;
; 46.498 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.431      ;
; 46.498 ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.431      ;
; 46.498 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.073     ; 3.431      ;
; 46.499 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[29]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.431      ;
; 46.499 ; add_32bit:u_add_32bit|add[2]                                                                                    ; add_32bit:u_add_32bit|add[31]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.072     ; 3.431      ;
; 46.508 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.075     ; 3.419      ;
+--------+-----------------------------------------------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                  ;
+-------+-------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                       ; To Node                                                                                                          ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.386 ; add_32bit:u_add_32bit|add[21]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.041      ;
; 0.454 ; add_32bit:u_add_32bit|add[29]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.109      ;
; 0.638 ; add_32bit:u_add_32bit|add[28]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.293      ;
; 0.649 ; add_32bit:u_add_32bit|add[21]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.304      ;
; 0.674 ; add_32bit:u_add_32bit|add[24]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.425      ; 1.329      ;
; 0.682 ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.951      ;
; 0.682 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.951      ;
; 0.683 ; add_32bit:u_add_32bit|add[25]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.434      ; 1.347      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.952      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.683 ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.951      ;
; 0.684 ; add_32bit:u_add_32bit|add[3]                    ; add_32bit:u_add_32bit|add[3]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.951      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.951      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.951      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.953      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.684 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.952      ;
; 0.685 ; add_32bit:u_add_32bit|add[15]                   ; add_32bit:u_add_32bit|add[15]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.952      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.952      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.071      ; 0.951      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.071      ; 0.951      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[17]                   ; add_32bit:u_add_32bit|add[17]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[13]                   ; add_32bit:u_add_32bit|add[13]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[11]                   ; add_32bit:u_add_32bit|add[11]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[6]                    ; add_32bit:u_add_32bit|add[6]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; add_32bit:u_add_32bit|add[5]                    ; add_32bit:u_add_32bit|add[5]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.953      ;
; 0.686 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.071      ; 0.952      ;
; 0.687 ; add_32bit:u_add_32bit|add[19]                   ; add_32bit:u_add_32bit|add[19]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[18]                   ; add_32bit:u_add_32bit|add[18]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[12]                   ; add_32bit:u_add_32bit|add[12]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[9]                    ; add_32bit:u_add_32bit|add[9]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[8]                    ; add_32bit:u_add_32bit|add[8]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; add_32bit:u_add_32bit|add[7]                    ; add_32bit:u_add_32bit|add[7]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.954      ;
; 0.687 ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.071      ; 0.953      ;
; 0.687 ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.956      ;
; 0.688 ; add_32bit:u_add_32bit|add[29]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.434      ; 1.352      ;
; 0.688 ; add_32bit:u_add_32bit|add[1]                    ; add_32bit:u_add_32bit|add[1]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.955      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.957      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.688 ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[16]                   ; add_32bit:u_add_32bit|add[16]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[14]                   ; add_32bit:u_add_32bit|add[14]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.956      ;
; 0.689 ; add_32bit:u_add_32bit|add[2]                    ; add_32bit:u_add_32bit|add[2]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.956      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.956      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.957      ;
; 0.690 ; add_32bit:u_add_32bit|add[10]                   ; add_32bit:u_add_32bit|add[10]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.957      ;
; 0.690 ; add_32bit:u_add_32bit|add[4]                    ; add_32bit:u_add_32bit|add[4]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.957      ;
; 0.690 ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.957      ;
; 0.690 ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.071      ; 0.956      ;
; 0.691 ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.071      ; 0.957      ;
; 0.703 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15] ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.972      ;
; 0.703 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13] ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kh[11] ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15] ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13] ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15] ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kl[13] ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kh[29] ; key_con:u_key_con|key_delay:u_key2_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kh[21] ; key_con:u_key_con|key_delay:u_key2_delay|kh[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kh[19] ; key_con:u_key_con|key_delay:u_key2_delay|kh[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kh[15] ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.704 ; key_con:u_key_con|key_delay:u_key2_delay|kh[13] ; key_con:u_key_con|key_delay:u_key2_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.972      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kl[29] ; key_con:u_key_con|key_delay:u_key3_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kl[21] ; key_con:u_key_con|key_delay:u_key3_delay|kl[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kl[19] ; key_con:u_key_con|key_delay:u_key3_delay|kl[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kl[15] ; key_con:u_key_con|key_delay:u_key3_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.972      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key3_delay|kl[13] ; key_con:u_key_con|key_delay:u_key3_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.972      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kh[29] ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kh[21] ; key_con:u_key_con|key_delay:u_key1_delay|kh[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kh[19] ; key_con:u_key_con|key_delay:u_key1_delay|kh[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kl[29] ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kl[21] ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kl[19] ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key1_delay|kl[11] ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kl[29] ; key_con:u_key_con|key_delay:u_key2_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kl[21] ; key_con:u_key_con|key_delay:u_key2_delay|kl[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kl[19] ; key_con:u_key_con|key_delay:u_key2_delay|kl[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kl[11] ; key_con:u_key_con|key_delay:u_key2_delay|kl[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kh[27] ; key_con:u_key_con|key_delay:u_key2_delay|kh[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.974      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kh[17] ; key_con:u_key_con|key_delay:u_key2_delay|kh[17]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.074      ; 0.974      ;
; 0.705 ; key_con:u_key_con|key_delay:u_key2_delay|kh[11] ; key_con:u_key_con|key_delay:u_key2_delay|kh[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.973      ;
; 0.706 ; key_con:u_key_con|key_delay:u_key3_delay|kl[27] ; key_con:u_key_con|key_delay:u_key3_delay|kl[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.974      ;
; 0.706 ; key_con:u_key_con|key_delay:u_key3_delay|kl[17] ; key_con:u_key_con|key_delay:u_key3_delay|kl[17]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.073      ; 0.974      ;
; 0.706 ; key_con:u_key_con|key_delay:u_key3_delay|kl[11] ; key_con:u_key_con|key_delay:u_key3_delay|kl[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.973      ;
; 0.706 ; key_con:u_key_con|key_delay:u_key3_delay|kh[29] ; key_con:u_key_con|key_delay:u_key3_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.973      ;
; 0.706 ; key_con:u_key_con|key_delay:u_key3_delay|kh[21] ; key_con:u_key_con|key_delay:u_key3_delay|kh[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.973      ;
; 0.706 ; key_con:u_key_con|key_delay:u_key3_delay|kh[19] ; key_con:u_key_con|key_delay:u_key3_delay|kh[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.072      ; 0.973      ;
; 0.706 ; key_con:u_key_con|key_delay:u_key3_delay|kh[15] ; key_con:u_key_con|key_delay:u_key3_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.071      ; 0.972      ;
+-------+-------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 1.616 ; 6.060        ; 4.444          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.943  ; 9.943        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.975  ; 9.975        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.991  ; 9.991        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.008 ; 10.008       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.025 ; 10.025       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.057 ; 10.057       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock                                                 ; Clock Edge ; Target                                          ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------+
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[10] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[11] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[12] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[13] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[14] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[15] ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[8]  ;
; 24.718 ; 24.934       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kl[9]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[10] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[11] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[12] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[13] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[14] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[15] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[16] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[17] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[18] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[19] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[20] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[21] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[22] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[23] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[24] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[25] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[26] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[27] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[28] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[29] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[30] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[7]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[8]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kh[9]  ;
; 24.719 ; 24.935       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key3_delay|kout   ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[10] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[11] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[12] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[13] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[14] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[15] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[7]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[8]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kh[9]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[10] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[11] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[12] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[13] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[14] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[15] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[16] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[17] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[18] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[19] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[20] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[21] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[22] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[23] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[24] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[25] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[26] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[27] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[28] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[29] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[30] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[31] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[7]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[8]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kh[9]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]  ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[10] ;
; 24.720 ; 24.936       ; 0.216          ; High Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[11] ;
+--------+--------------+----------------+------------------+-------------------------------------------------------+------------+-------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 3.342 ; 3.836 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 2.511 ; 2.663 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 3.342 ; 3.836 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 2.988 ; 3.261 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 2.542 ; 2.909 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -1.744 ; -1.985 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -1.744 ; -1.985 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -2.300 ; -2.722 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -1.964 ; -2.297 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -1.693 ; -1.955 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                           ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 2.980 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 8.979 ; 8.800 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 6.869 ; 6.621 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 7.647 ; 7.371 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 7.802 ; 7.510 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 7.934 ; 7.651 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 7.916 ; 7.620 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 8.979 ; 8.800 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 7.774 ; 7.529 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 7.916 ; 7.649 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 7.975 ; 7.716 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 8.099 ; 7.808 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 8.112 ; 7.843 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 8.556 ; 8.250 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 8.623 ; 8.324 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 8.606 ; 8.208 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 2.903 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 2.523 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 5.490 ; 5.263 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 5.923 ; 5.748 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 6.101 ; 5.769 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 5.766 ; 5.494 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 5.794 ; 5.525 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 5.757 ; 5.482 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 6.775 ; 6.609 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 5.490 ; 5.263 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 5.569 ; 5.316 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 5.550 ; 5.309 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 5.596 ; 5.319 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 5.578 ; 5.327 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 5.907 ; 5.616 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 5.737 ; 5.457 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 6.099 ; 5.666 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 2.447 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Slow 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup Summary                                             ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 46.698 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold Summary                                             ;
+-------------------------------------------------------+-------+---------------+
; Clock                                                 ; Slack ; End Point TNS ;
+-------------------------------------------------------+-------+---------------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.135 ; 0.000         ;
+-------------------------------------------------------+-------+---------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary                               ;
+-------------------------------------------------------+--------+---------------+
; Clock                                                 ; Slack  ; End Point TNS ;
+-------------------------------------------------------+--------+---------------+
; CLK_165M                                              ; 2.060  ; 0.000         ;
; CLK_50M                                               ; 9.594  ; 0.000         ;
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 24.734 ; 0.000         ;
+-------------------------------------------------------+--------+---------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                                                                                  ;
+--------+-----------------------------------------------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                                                                                       ; To Node                                                                                                          ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+--------+-----------------------------------------------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 46.698 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.224     ; 3.065      ;
; 46.819 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.225     ; 2.943      ;
; 47.055 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.225     ; 2.707      ;
; 47.088 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.223     ; 2.676      ;
; 47.164 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[6]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.224     ; 2.599      ;
; 47.399 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[5]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.223     ; 2.365      ;
; 47.508 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.218     ; 2.261      ;
; 47.637 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[10]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.218     ; 2.132      ;
; 47.650 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[12]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.217     ; 2.120      ;
; 47.662 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[11]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.218     ; 2.107      ;
; 47.788 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0 ; sel_wave:u_sel_wave|da_out_reg[13]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.217     ; 1.982      ;
; 47.816 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.218     ; 1.953      ;
; 47.825 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.218     ; 1.944      ;
; 47.833 ; key_con:u_key_con|key_delay:u_key3_delay|kh[22]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.017     ; 2.137      ;
; 47.979 ; key_con:u_key_con|key_delay:u_key3_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.019     ; 1.989      ;
; 47.988 ; key_con:u_key_con|key_delay:u_key3_delay|kh[21]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.017     ; 1.982      ;
; 47.994 ; key_con:u_key_con|key_delay:u_key2_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key2_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.956      ;
; 48.011 ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0  ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.218     ; 1.758      ;
; 48.035 ; key_con:u_key_con|key_delay:u_key2_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.915      ;
; 48.048 ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.017     ; 1.922      ;
; 48.051 ; key_con:u_key_con|key_delay:u_key3_delay|kh[20]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.017     ; 1.919      ;
; 48.058 ; add_32bit:u_add_32bit|add[23]                                                                                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; 0.144      ; 2.095      ;
; 48.058 ; key_con:u_key_con|key_delay:u_key3_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.019     ; 1.910      ;
; 48.065 ; add_32bit:u_add_32bit|add[23]                                                                                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; 0.144      ; 2.088      ;
; 48.073 ; key_con:u_key_con|key_delay:u_key3_delay|kl[14]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.019     ; 1.895      ;
; 48.088 ; key_con:u_key_con|key_delay:u_key2_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.862      ;
; 48.098 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[2]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.852      ;
; 48.101 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[13]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.849      ;
; 48.101 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[9]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.849      ;
; 48.101 ; key_con:u_key_con|key_delay:u_key3_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.019     ; 1.867      ;
; 48.121 ; key_con:u_key_con|key_delay:u_key3_delay|kl[9]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.019     ; 1.847      ;
; 48.128 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.822      ;
; 48.129 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[12]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.821      ;
; 48.129 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[11]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.821      ;
; 48.130 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[8]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.820      ;
; 48.130 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[4]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.820      ;
; 48.131 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[10]                                                                               ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.819      ;
; 48.132 ; key_con:u_key_con|key_delay:u_key3_delay|kh[29]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.017     ; 1.838      ;
; 48.133 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[3]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.817      ;
; 48.134 ; add_32bit:u_add_32bit|add[31]                                                                                   ; sel_wave:u_sel_wave|da_out_reg[7]                                                                                ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.816      ;
; 48.140 ; key_con:u_key_con|key_delay:u_key3_delay|kh[19]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.017     ; 1.830      ;
; 48.145 ; key_con:u_key_con|key_delay:u_key3_delay|kh[10]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.021     ; 1.821      ;
; 48.169 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.778      ;
; 48.172 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.778      ;
; 48.172 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.778      ;
; 48.172 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.778      ;
; 48.173 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[31]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.778      ;
; 48.173 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.774      ;
; 48.173 ; key_con:u_key_con|key_delay:u_key3_delay|kl[15]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.019     ; 1.795      ;
; 48.173 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.778      ;
; 48.176 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.774      ;
; 48.176 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.774      ;
; 48.176 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.774      ;
; 48.177 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[30]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.774      ;
; 48.177 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.774      ;
; 48.180 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.029     ; 1.778      ;
; 48.180 ; key_con:u_key_con|key_delay:u_key2_delay|kl[8]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.770      ;
; 48.183 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.764      ;
; 48.184 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.029     ; 1.774      ;
; 48.186 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.764      ;
; 48.186 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.764      ;
; 48.186 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.764      ;
; 48.187 ; key_con:u_key_con|key_delay:u_key3_delay|kh[27]                                                                 ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.017     ; 1.783      ;
; 48.187 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[31]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.764      ;
; 48.187 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.764      ;
; 48.192 ; key_con:u_key_con|key_delay:u_key3_delay|kl[7]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.019     ; 1.776      ;
; 48.194 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.029     ; 1.764      ;
; 48.204 ; key_con:u_key_con|key_delay:u_key3_delay|kh[7]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.021     ; 1.762      ;
; 48.221 ; key_con:u_key_con|key_delay:u_key3_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.726      ;
; 48.221 ; key_con:u_key_con|key_delay:u_key1_delay|kl[10]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.729      ;
; 48.223 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                 ; key_con:u_key_con|key_delay:u_key1_delay|kout                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.727      ;
; 48.224 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.726      ;
; 48.224 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.726      ;
; 48.224 ; key_con:u_key_con|key_delay:u_key2_delay|kh[0]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.726      ;
; 48.225 ; add_32bit:u_add_32bit|add[0]                                                                                    ; add_32bit:u_add_32bit|add[30]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.726      ;
; 48.225 ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.726      ;
; 48.232 ; key_con:u_key_con|key_delay:u_key3_delay|kl[0]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kl[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.029     ; 1.726      ;
; 48.237 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.710      ;
; 48.239 ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.708      ;
; 48.240 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.710      ;
; 48.240 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.710      ;
; 48.240 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.710      ;
; 48.241 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[29]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.710      ;
; 48.241 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[28]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.706      ;
; 48.241 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.710      ;
; 48.242 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.708      ;
; 48.242 ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.708      ;
; 48.242 ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.708      ;
; 48.243 ; add_32bit:u_add_32bit|add[3]                                                                                    ; add_32bit:u_add_32bit|add[31]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.708      ;
; 48.243 ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]                                                                  ; key_con:u_key_con|key_delay:u_key3_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.040     ; 1.704      ;
; 48.243 ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.708      ;
; 48.244 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[28]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.706      ;
; 48.244 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[28]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.706      ;
; 48.244 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[28]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.706      ;
; 48.245 ; add_32bit:u_add_32bit|add[1]                                                                                    ; add_32bit:u_add_32bit|add[28]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.706      ;
; 48.245 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kl[28]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.706      ;
; 48.246 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                  ; key_con:u_key_con|key_delay:u_key1_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.704      ;
; 48.246 ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kl[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.704      ;
; 48.246 ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                  ; key_con:u_key_con|key_delay:u_key2_delay|kh[30]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.037     ; 1.704      ;
; 48.247 ; add_32bit:u_add_32bit|add[3]                                                                                    ; add_32bit:u_add_32bit|add[30]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 50.000       ; -0.036     ; 1.704      ;
+--------+-----------------------------------------------------------------------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                                                                                                                  ;
+-------+-------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; Slack ; From Node                                       ; To Node                                                                                                          ; Launch Clock                                          ; Latch Clock                                           ; Relationship ; Clock Skew ; Data Delay ;
+-------+-------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+
; 0.135 ; add_32bit:u_add_32bit|add[21]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.463      ;
; 0.172 ; add_32bit:u_add_32bit|add[29]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.500      ;
; 0.267 ; add_32bit:u_add_32bit|add[21]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.595      ;
; 0.269 ; add_32bit:u_add_32bit|add[28]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.597      ;
; 0.283 ; add_32bit:u_add_32bit|add[24]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.224      ; 0.611      ;
; 0.284 ; add_32bit:u_add_32bit|add[25]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.229      ; 0.617      ;
; 0.285 ; add_32bit:u_add_32bit|add[29]                   ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0 ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.229      ; 0.618      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.292 ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.413      ;
; 0.293 ; add_32bit:u_add_32bit|add[15]                   ; add_32bit:u_add_32bit|add[15]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; add_32bit:u_add_32bit|add[3]                    ; add_32bit:u_add_32bit|add[3]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.413      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.293 ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[19]                   ; add_32bit:u_add_32bit|add[19]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[18]                   ; add_32bit:u_add_32bit|add[18]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[17]                   ; add_32bit:u_add_32bit|add[17]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[16]                   ; add_32bit:u_add_32bit|add[16]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[13]                   ; add_32bit:u_add_32bit|add[13]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[12]                   ; add_32bit:u_add_32bit|add[12]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[11]                   ; add_32bit:u_add_32bit|add[11]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[9]                    ; add_32bit:u_add_32bit|add[9]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[8]                    ; add_32bit:u_add_32bit|add[8]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[7]                    ; add_32bit:u_add_32bit|add[7]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[6]                    ; add_32bit:u_add_32bit|add[6]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[5]                    ; add_32bit:u_add_32bit|add[5]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; add_32bit:u_add_32bit|add[1]                    ; add_32bit:u_add_32bit|add[1]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.294 ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key2_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[14]                   ; add_32bit:u_add_32bit|add[14]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[4]                    ; add_32bit:u_add_32bit|add[4]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; add_32bit:u_add_32bit|add[2]                    ; add_32bit:u_add_32bit|add[2]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]  ; key_con:u_key_con|key_delay:u_key3_delay|kh[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.295 ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]  ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.415      ;
; 0.296 ; add_32bit:u_add_32bit|add[10]                   ; add_32bit:u_add_32bit|add[10]                                                                                    ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.416      ;
; 0.299 ; add_32bit:u_add_32bit|add[0]                    ; add_32bit:u_add_32bit|add[0]                                                                                     ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.419      ;
; 0.302 ; key_con:u_key_con|key_delay:u_key1_delay|kh[15] ; key_con:u_key_con|key_delay:u_key1_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.423      ;
; 0.302 ; key_con:u_key_con|key_delay:u_key2_delay|kl[15] ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.423      ;
; 0.302 ; key_con:u_key_con|key_delay:u_key2_delay|kh[15] ; key_con:u_key_con|key_delay:u_key2_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.423      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key3_delay|kl[31] ; key_con:u_key_con|key_delay:u_key3_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key3_delay|kl[15] ; key_con:u_key_con|key_delay:u_key3_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.423      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key3_delay|kh[15] ; key_con:u_key_con|key_delay:u_key3_delay|kh[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.423      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key1_delay|kh[13] ; key_con:u_key_con|key_delay:u_key1_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key1_delay|kl[15] ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.423      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key2_delay|kl[31] ; key_con:u_key_con|key_delay:u_key2_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key2_delay|kl[13] ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key2_delay|kh[31] ; key_con:u_key_con|key_delay:u_key2_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; key_con:u_key_con|key_delay:u_key2_delay|kh[13] ; key_con:u_key_con|key_delay:u_key2_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.424      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[29] ; key_con:u_key_con|key_delay:u_key3_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[27] ; key_con:u_key_con|key_delay:u_key3_delay|kl[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[21] ; key_con:u_key_con|key_delay:u_key3_delay|kl[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[19] ; key_con:u_key_con|key_delay:u_key3_delay|kl[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[17] ; key_con:u_key_con|key_delay:u_key3_delay|kl[17]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kl[13] ; key_con:u_key_con|key_delay:u_key3_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kh[31] ; key_con:u_key_con|key_delay:u_key3_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key3_delay|kh[13] ; key_con:u_key_con|key_delay:u_key3_delay|kh[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[31] ; key_con:u_key_con|key_delay:u_key1_delay|kh[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[11] ; key_con:u_key_con|key_delay:u_key1_delay|kh[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[7]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[7]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]  ; key_con:u_key_con|key_delay:u_key1_delay|kh[0]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[31] ; key_con:u_key_con|key_delay:u_key1_delay|kl[31]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key1_delay|kl[13] ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key2_delay|kl[29] ; key_con:u_key_con|key_delay:u_key2_delay|kl[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key2_delay|kl[27] ; key_con:u_key_con|key_delay:u_key2_delay|kl[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key2_delay|kl[21] ; key_con:u_key_con|key_delay:u_key2_delay|kl[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key2_delay|kl[19] ; key_con:u_key_con|key_delay:u_key2_delay|kl[19]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key2_delay|kl[17] ; key_con:u_key_con|key_delay:u_key2_delay|kl[17]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key2_delay|kl[11] ; key_con:u_key_con|key_delay:u_key2_delay|kl[11]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key2_delay|kl[7]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[7]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]  ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                   ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key2_delay|kh[29] ; key_con:u_key_con|key_delay:u_key2_delay|kh[29]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key2_delay|kh[27] ; key_con:u_key_con|key_delay:u_key2_delay|kh[27]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; key_con:u_key_con|key_delay:u_key2_delay|kh[21] ; key_con:u_key_con|key_delay:u_key2_delay|kh[21]                                                                  ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000        ; 0.037      ; 0.425      ;
+-------+-------------------------------------------------+------------------------------------------------------------------------------------------------------------------+-------------------------------------------------------+-------------------------------------------------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_165M'                                     ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; Slack ; Actual Width ; Required Width ; Type      ; Clock    ; Clock Edge ; Target       ;
+-------+--------------+----------------+-----------+----------+------------+--------------+
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_CLK      ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[0]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[10] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[11] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[12] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[13] ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[1]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[2]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[3]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[4]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[5]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[6]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[7]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[8]  ;
; 2.060 ; 6.060        ; 4.000          ; Port Rate ; CLK_165M ; Rise       ; DAC_DATA[9]  ;
+-------+--------------+----------------+-----------+----------+------------+--------------+


+----------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'CLK_50M'                                                                                                ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type             ; Clock   ; Clock Edge ; Target                                                          ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 9.594  ; 9.594        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 9.620  ; 9.620        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 9.622  ; 9.622        ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.000 ; 10.000       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.000 ; 10.000       ; 0.000          ; Low Pulse Width  ; CLK_50M ; Rise       ; SYS_CLK~input|i                                                 ;
; 10.378 ; 10.378       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]         ;
; 10.380 ; 10.380       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; SYS_CLK~input|o                                                 ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]           ;
; 10.405 ; 10.405       ; 0.000          ; High Pulse Width ; CLK_50M ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|observablevcoout ;
; 16.000 ; 20.000       ; 4.000          ; Port Rate        ; CLK_50M ; Rise       ; SYS_CLK                                                         ;
+--------+--------------+----------------+------------------+---------+------------+-----------------------------------------------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width: 'u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]'                                                                                                                                                 ;
+--------+--------------+----------------+-----------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; Slack  ; Actual Width ; Required Width ; Type            ; Clock                                                 ; Clock Edge ; Target                                                                                                            ;
+--------+--------------+----------------+-----------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a8~porta_address_reg0  ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a12~porta_address_reg0   ;
; 24.734 ; 24.964       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a2~porta_address_reg0    ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[8]                           ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[9]                           ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a0~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a10~porta_address_reg0 ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a12~porta_address_reg0 ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a2~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a4~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|ram_block1a6~porta_address_reg0  ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a0~porta_address_reg0    ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a10~porta_address_reg0   ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a4~porta_address_reg0    ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a6~porta_address_reg0    ;
; 24.735 ; 24.965       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Tri:ROM_Tri|altsyncram:altsyncram_component|altsyncram_4aa1:auto_generated|ram_block1a8~porta_address_reg0    ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[0]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[10]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[11]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[12]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[13]                          ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[1]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[2]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[3]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[4]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[5]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[6]                           ;
; 24.736 ; 24.966       ; 0.230          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; ROM_Sin:u_ROM_Sin|altsyncram:altsyncram_component|altsyncram_aj91:auto_generated|q_a[7]                           ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[0]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[10]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[11]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[12]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[13]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[14]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[15]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[16]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[17]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[18]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[19]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[1]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[20]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[21]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[22]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[23]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[24]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[25]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[26]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[27]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[28]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[29]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[2]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[30]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[31]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[3]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[4]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[5]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[6]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[7]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[8]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key1_delay|kl[9]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[0]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[10]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[11]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[12]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[13]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[14]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[15]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[16]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[17]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[18]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[19]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[1]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[20]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[21]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[22]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[23]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[24]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[25]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[26]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[27]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[28]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[29]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[2]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[30]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[31]                                                                   ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[3]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[4]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[5]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[6]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[7]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[8]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|key_delay:u_key2_delay|kl[9]                                                                    ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|sel[0]                                                                                          ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; key_con:u_key_con|sel[1]                                                                                          ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[0]                                                                                 ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[10]                                                                                ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[11]                                                                                ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[12]                                                                                ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[13]                                                                                ;
; 24.797 ; 24.981       ; 0.184          ; Low Pulse Width ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; Rise       ; sel_wave:u_sel_wave|da_out_reg[1]                                                                                 ;
+--------+--------------+----------------+-----------------+-------------------------------------------------------+------------+-------------------------------------------------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 1.882 ; 2.132 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 1.435 ; 1.715 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 1.882 ; 2.132 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 1.685 ; 1.940 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 1.435 ; 1.780 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -1.037 ; -1.370 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -1.037 ; -1.370 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -1.322 ; -1.631 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -1.133 ; -1.473 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -0.996 ; -1.356 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                           ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 1.519 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 4.527 ; 4.637 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 3.241 ; 3.360 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 3.533 ; 3.606 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 3.658 ; 3.722 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 3.687 ; 3.762 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 3.727 ; 3.779 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 4.527 ; 4.637 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 3.671 ; 3.716 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 3.714 ; 3.778 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 3.781 ; 3.845 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 3.800 ; 3.860 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 3.848 ; 3.909 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 4.027 ; 4.115 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 4.105 ; 4.191 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 4.052 ; 4.116 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 1.584 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 1.289 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 2.559 ; 2.616 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 2.801 ; 2.881 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 2.817 ; 2.901 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 2.679 ; 2.755 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 2.696 ; 2.775 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 2.677 ; 2.739 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 3.503 ; 3.617 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 2.559 ; 2.616 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 2.592 ; 2.668 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 2.587 ; 2.663 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 2.595 ; 2.667 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 2.600 ; 2.675 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 2.748 ; 2.847 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 2.688 ; 2.786 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 2.783 ; 2.888 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 1.352 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


---------------------------------------------
; Fast 1200mV 0C Model Metastability Report ;
---------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                                                                ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Clock                                                  ; Setup  ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+
; Worst-case Slack                                       ; 42.303 ; 0.135 ; N/A      ; N/A     ; 1.616               ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 1.616               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 9.594               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 42.303 ; 0.135 ; N/A      ; N/A     ; 24.718              ;
; Design-wide TNS                                        ; 0.0    ; 0.0   ; 0.0      ; 0.0     ; 0.0                 ;
;  CLK_165M                                              ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  CLK_50M                                               ; N/A    ; N/A   ; N/A      ; N/A     ; 0.000               ;
;  u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 0.000  ; 0.000 ; N/A      ; N/A     ; 0.000               ;
+--------------------------------------------------------+--------+-------+----------+---------+---------------------+


+--------------------------------------------------------------------------------------------------------------+
; Setup Times                                                                                                  ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; 3.802 ; 4.103 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; 2.847 ; 2.941 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; 3.802 ; 4.103 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; 3.391 ; 3.549 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; 2.881 ; 3.187 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+-------+-------+------------+-------------------------------------------------------+


+----------------------------------------------------------------------------------------------------------------+
; Hold Times                                                                                                     ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; Data Port  ; Clock Port ; Rise   ; Fall   ; Clock Edge ; Clock Reference                                       ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+
; KEY_IN[*]  ; CLK_50M    ; -1.037 ; -1.370 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[0] ; CLK_50M    ; -1.037 ; -1.370 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[1] ; CLK_50M    ; -1.322 ; -1.631 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  KEY_IN[2] ; CLK_50M    ; -1.133 ; -1.473 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; SYS_RST    ; CLK_50M    ; -0.996 ; -1.356 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+------------+------------+--------+--------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Clock to Output Times                                                                                           ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 3.199 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 9.726 ; 9.653 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 7.289 ; 7.157 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 8.152 ; 7.966 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 8.431 ; 8.211 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 8.494 ; 8.291 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 8.564 ; 8.342 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 9.726 ; 9.653 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 8.441 ; 8.250 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 8.522 ; 8.347 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 8.639 ; 8.478 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 8.731 ; 8.530 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 8.804 ; 8.618 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 9.227 ; 9.021 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 9.346 ; 9.159 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 9.310 ; 9.023 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 3.157 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Minimum Clock to Output Times                                                                                   ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; Data Port     ; Clock Port ; Rise  ; Fall  ; Clock Edge ; Clock Reference                                       ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+
; DAC_CLK       ; CLK_50M    ; 1.289 ;       ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_DATA[*]   ; CLK_50M    ; 2.559 ; 2.616 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[0]  ; CLK_50M    ; 2.801 ; 2.881 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[1]  ; CLK_50M    ; 2.817 ; 2.901 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[2]  ; CLK_50M    ; 2.679 ; 2.755 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[3]  ; CLK_50M    ; 2.696 ; 2.775 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[4]  ; CLK_50M    ; 2.677 ; 2.739 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[5]  ; CLK_50M    ; 3.503 ; 3.617 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[6]  ; CLK_50M    ; 2.559 ; 2.616 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[7]  ; CLK_50M    ; 2.592 ; 2.668 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[8]  ; CLK_50M    ; 2.587 ; 2.663 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[9]  ; CLK_50M    ; 2.595 ; 2.667 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[10] ; CLK_50M    ; 2.600 ; 2.675 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[11] ; CLK_50M    ; 2.748 ; 2.847 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[12] ; CLK_50M    ; 2.688 ; 2.786 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
;  DAC_DATA[13] ; CLK_50M    ; 2.783 ; 2.888 ; Rise       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
; DAC_CLK       ; CLK_50M    ;       ; 1.352 ; Fall       ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ;
+---------------+------------+-------+-------+------------+-------------------------------------------------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                    ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin           ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; PD            ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_CLK       ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[0]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[1]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[2]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[3]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[4]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[5]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[6]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[7]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[8]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[9]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[10]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[11]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[12]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; DAC_DATA[13]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; SYS_RST                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; SYS_CLK                 ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[0]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[2]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; KEY_IN[1]               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD            ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.74e-09 V                   ; 2.37 V              ; -0.0346 V           ; 0.198 V                              ; 0.094 V                              ; 3.14e-10 s                  ; 2.92e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.74e-09 V                  ; 2.37 V             ; -0.0346 V          ; 0.198 V                             ; 0.094 V                             ; 3.14e-10 s                 ; 2.92e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD            ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_CLK       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[8]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[9]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[10]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[11]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[12]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[13]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.55e-07 V                   ; 2.35 V              ; -0.00221 V          ; 0.097 V                              ; 0.005 V                              ; 4.49e-10 s                  ; 3.85e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.55e-07 V                  ; 2.35 V             ; -0.00221 V         ; 0.097 V                             ; 0.005 V                             ; 4.49e-10 s                 ; 3.85e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; PD            ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_CLK       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; DAC_DATA[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; DAC_DATA[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[8]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[9]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[10]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[11]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[12]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; DAC_DATA[13]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.22e-08 V                   ; 2.74 V              ; -0.06 V             ; 0.158 V                              ; 0.08 V                               ; 2.68e-10 s                  ; 2.19e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.22e-08 V                  ; 2.74 V             ; -0.06 V            ; 0.158 V                             ; 0.08 V                              ; 2.68e-10 s                 ; 2.19e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Setup Transfers                                                                                                                                           ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 4115     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+-----------------------------------------------------------------------------------------------------------------------------------------------------------+
; Hold Transfers                                                                                                                                            ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; From Clock                                            ; To Clock                                              ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] ; 4115     ; 0        ; 0        ; 0        ;
+-------------------------------------------------------+-------------------------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No non-DPA dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths                            ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 2     ; 2    ;
; Unconstrained Input Ports       ; 4     ; 4    ;
; Unconstrained Input Port Paths  ; 272   ; 272  ;
; Unconstrained Output Ports      ; 15    ; 15   ;
; Unconstrained Output Port Paths ; 119   ; 119  ;
+---------------------------------+-------+------+


+------------------------------------+
; TimeQuest Timing Analyzer Messages ;
+------------------------------------+
Info: *******************************************************************
Info: Running Quartus II 64-Bit TimeQuest Timing Analyzer
    Info: Version 13.1.0 Build 162 10/23/2013 SJ Web Edition
    Info: Processing started: Thu Jul 31 21:11:30 2025
Info: Command: quartus_sta DAC904 -c DAC904
Info: qsta_default_script.tcl version: #1
Warning (20028): Parallel compilation is not licensed and has been disabled
Info (21077): Low junction temperature is 0 degrees C
Info (21077): High junction temperature is 85 degrees C
Info (332104): Reading SDC File: '../doc/SDC1.sdc'
Info (332110): Deriving PLL clocks
    Info (332110): create_generated_clock -source {u_PLL_CLK|altpll_component|auto_generated|pll1|inclk[0]} -divide_by 5 -multiply_by 2 -duty_cycle 50.00 -name {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]} {u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0]}
Info (332151): Clock uncertainty is not calculated until you update the timing netlist.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key2_delay|kout was determined to be a clock but was found without an associated clock assignment.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key1_delay|kout was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info: Found TIMEQUEST_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON
Info: Analyzing Slow 1200mV 85C Model
Info (332146): Worst-case setup slack is 42.303
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    42.303               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.408
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.408               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.934               0.000 CLK_50M 
    Info (332119):    24.719               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Slow 1200mV 0C Model
Info (334003): Started post-fitting delay annotation
Info (334004): Delay annotation completed successfully
Warning (332060): Node: key_con:u_key_con|key_delay:u_key2_delay|kout was determined to be a clock but was found without an associated clock assignment.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key1_delay|kout was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 42.849
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    42.849               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.386
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.386               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 1.616
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     1.616               0.000 CLK_165M 
    Info (332119):     9.943               0.000 CLK_50M 
    Info (332119):    24.718               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info: Analyzing Fast 1200mV 0C Model
Warning (332060): Node: key_con:u_key_con|key_delay:u_key2_delay|kout was determined to be a clock but was found without an associated clock assignment.
Warning (332060): Node: key_con:u_key_con|key_delay:u_key1_delay|kout was determined to be a clock but was found without an associated clock assignment.
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in TimeQuest to see clock uncertainties.
Info (332146): Worst-case setup slack is 46.698
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    46.698               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332146): Worst-case hold slack is 0.135
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.135               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is 2.060
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     2.060               0.000 CLK_165M 
    Info (332119):     9.594               0.000 CLK_50M 
    Info (332119):    24.734               0.000 u_PLL_CLK|altpll_component|auto_generated|pll1|clk[0] 
Info (332102): Design is not fully constrained for setup requirements
Info (332102): Design is not fully constrained for hold requirements
Info: Quartus II 64-Bit TimeQuest Timing Analyzer was successful. 0 errors, 7 warnings
    Info: Peak virtual memory: 4634 megabytes
    Info: Processing ended: Thu Jul 31 21:11:31 2025
    Info: Elapsed time: 00:00:01
    Info: Total CPU time (on all processors): 00:00:01


