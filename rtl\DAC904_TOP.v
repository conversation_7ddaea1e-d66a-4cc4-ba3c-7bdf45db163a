module DAC904_TOP(
	input  wire 			 SYS_CLK	,
	input	 wire 			 SYS_RST	,
	input	 wire	[2:0]	 KEY_IN		,
	output wire				 PD				,
	output wire				 DAC_CLK	,
	output wire	[13:0] DAC_DATA
);

//DAC904闂傚倷鐒﹂惇褰掑礉瀹€鈧埀顒佸嚬閸ㄥ啿鈻庨姀銈庢晣闁靛繒濮烽ˇ鈺呮⒑閹呯妞ゎ偄顦靛鎶藉煛閸涱喚鍘卞┑鈽嗗灟鐠€锕傚磿閹惧绠鹃柡澶嬪灦椤ャ垺顨ラ悙鏉戠伌濠殿喒鍋撻梺缁樕戣ぐ鍐緤妤ｅ啯鍊垫繛鍫濈仢閺嬫稑螖閻樿尙绠虫俊383 - DAC_DATA) * 闂傚倷鐒︾€笛囨儗16383闂傚倷鐒︾€笛呯矙閹次层劑鍩€椤掑倻纾奸弶鍫涘妿缁犳挻銇勯鐘茬伈濠碘€崇埣瀹曘劑顢橀悙鐢瞪戦梺璇插椤旀牠宕伴弽顐ｅ床婵☆垳鍘ч崹婵囥亜閺嶃劎鐭嬪┑顖氥偢閺屾洟宕煎┑鍡╀紑闂佽崵鍠愮换鍌炴箒闂佹寧绻傞悧濠勬兜閸洘鐓涘ù锝夋涧閳ь剚鐗曢…鍥疀濞戞顔掗梺鍛婂姇瀵爼鎯侀弮鍫熲拺閻炴稈鈧厖澹曢梻浣筋嚃閸ㄥ孩鎷呴柨瀣闯缂傚倸鍊搁崐鎼佸磹閻㈢鐤炬繛鎴欏灪閸庡秵銇勯幒鎴濐仾闁哄拋鍓涢埀顒€鍘滈崑鎾绘煃瑜滈崜鐔煎箖閿熺媭鏁囬柣妯哄暱绾绢垰顪冮妶鍡欏鐎光偓缁嬫鐎舵い鏇楀亾闁

wire	CLK_20M;

wire 	[1 :0] sel			;
wire 	[31:0] fre_k		;
wire 	[11:0] addr			;
wire 	[13:0] wave_z		;
wire 	[13:0] wave_s		;
wire 	[13:0] wave_f		;
wire	[13:0] DATA_BUF	;

// 闂傚倷绀侀幖顐﹀磹娴犲缍栧璺哄閻掑﹥绻濋棃娑卞剰闁哄绶氶弻锝呂旈埀顒勬偋閸℃瑧鐭
assign wave_f = addr[11] ? 14'b11_1111_1111_1111 : 14'b00_0000_0000_0000;

//闂傚倸鍊风粈渚€濡靛鈧幊妤冩崉婵傚€燁潐瀵板嫰骞囬鐐拌檸婵＄偑鍊栭悧妤佺▔鐟欏嫮鐟归柍褜鍓欓悾
PLL_CLK u_PLL_CLK(
	.areset	(!SYS_RST	),
	.inclk0	(SYS_CLK	),
	.c0			(CLK_20M	)
);

// 濠电姵顔栭崰妤冩崲閹邦喖绶ら柤鎭掑劘閸嬪﹪鏌嶉妷锔界伇闁哄鐗犻弻鏇＄疀婵犲倸鈷夊┑鐐茬墕閻栧ジ寮婚悢鐑樺珰闁炽儴娅曢悵顖滅磽娴ｄ粙鍝虹紒杈ㄦ範M闂
ROM_Sin u_ROM_Sin(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_z		)
);

// 婵犵數鍋為崹鍫曞箰閹间絸鍥箮閺傘倗绋忓┑掳鍊愰崑鎾绘煃缂佹ɑ顥堝┑顔瑰亾闂佹枼鏅涢崯顖烆敊瀹ュ鈷戠痪顓炴媼閸ゅ啴鏌涢敐搴＄仯婵″弶鍔曢…鎶藉礌閻溾偓闂
ROM_Tri ROM_Tri(
	.address(addr			),
	.clock	(CLK_20M	),
	.q			(wave_s		)
);

//闂傚倷绶氬濠氭⒔閸曨偒鐔嗘俊顖欒閻掍粙鏌涢幇鍏哥凹妞ゎ偅娲橀妵鍕冀閵娿劌顥濆銈庡亾缂嶄線寮
add_32bit u_add_32bit(
	.clk	(CLK_20M	),
	.rst	(SYS_RST	),
	.fr_k (fre_k		),
	.adder(addr			)
);

//闂傚倷绀佸﹢閬嶁€﹂崼銉嬪洭鎮界粙鎸庣€銈嗘尪閸ㄥ湱绮诲畷鍥ㄥ枑鐎广儱顦弰銉︿繆閵堝倸浜鹃柧浼欑秮閺屽秹濡烽妸锔惧涧闂
key_con u_key_con(
	.clk			(CLK_20M	),
	.rst_n		(SYS_RST	),
	.key1_in	(KEY_IN[0]),
	.key2_in	(KEY_IN[1]),
	.key3_in  (KEY_IN[2]),
	.sel_wave	(sel			),
	.fre_k		(fre_k		)
);

//濠电姷鏁搁崑娑⑺囬銏犲瀭鐟滅増甯楅崵瀣煟濡も偓閻楀﹦绱為崶顒佺厵闁诡垎鍐╂瘣濠殿噯绲介ˇ闈涱潖濞差亶鏁冮柕蹇婃櫆鏁堥梺璇插閸濆酣宕濆畝鍐航闂備胶鍋ㄩ崕鏉戔枍閺囥垹纾
sel_wave u_sel_wave(
	.clk		(CLK_20M	),
	.rst_n	(SYS_RST	),
	.sel		(sel			),
	.da_ina (wave_z		),
	.da_inb (wave_s		),
	.da_inc (wave_f		),
	.da_out (DATA_BUF	)
);

assign PD = 1'b0;
assign DAC_CLK  = CLK_20M;
localparam [7:0] SPI_VV = 8'd2;  // 骞呭害鎺у埗鍙傛暟锛V/SPI_VV
// 姝ｇ‘鐨勫箙搴︾缉鏀撅細淇濇寔淇″彿浠191涓轰腑蹇冿紝鍙缉鏀惧箙搴﹂儴鍒
// 鍏紡锛氳緭鍑= 涓績鍊+ (杈撳叆-涓績鍊/缂╂斁鍥犲瓙
assign DAC_DATA = 14'd8191 + (DATA_BUF - 14'd8191)/SPI_VV; // 浠191涓轰腑蹇冭繘琛屽箙搴︾缉鏀
//assign DAC_DATA = 14'd10000; // 

endmodule
